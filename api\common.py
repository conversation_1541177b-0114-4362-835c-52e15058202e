from pydantic import BaseModel
from typing import Optional, Any, Dict, List

class StandardResponse(BaseModel):
    """
    Standard response format for all API endpoints.
    
    Format:
    {
        "message": "",
        "error": false,
        "status": 200,
        "data": {},
        ...additional_fields
    }
    """
    message: str = ""
    error: bool = False
    status: int = 200
    data: Any = None
    # Allow additional fields for future extensibility
    class Config:
        extra = "allow"
        json_schema_extra = {
            "example": {
                "message": "Operation completed successfully",
                "error": False,
                "status": 200,
                "data": {}
            }
        }

class StandardErrorResponse(BaseModel):
    """
    Standard error response format for all API endpoints.
    
    Format:
    {
        "message": "",
        "error": true,
        "status": 400,
        "data": {},
        ...additional_fields
    }
    """
    message: str = ""
    error: bool = True
    status: int = 400
    data: Any = None
    # Allow additional fields for future extensibility
    class Config:
        extra = "allow"
        json_schema_extra = {
            "example": {
                "message": "An error occurred",
                "error": True,
                "status": 400,
                "data": {}
            }
        }