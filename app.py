#!/usr/bin/env python3
"""
Main application entry point.

This is the main function to start the program as requested in the folder structure proposal.
"""

import uvicorn
import sys
import asyncio
from contextlib import asynccontextmanager
from utils.config_loader import ConfigLoader
from utils.logger import Logger
from api.routes import create_app
from services.database import DatabaseService
from services.background_scraper import BackgroundScraperService
from services.langchain_processor import LangchainProcessorService
from services.llm_service import LLMService
from services.langgraph_service import LanggraphService

# Initialize logger
app_logger = Logger("app")

async def initialize_services(config):
    """Initialize all services asynchronously"""
    # Initialize database service
    db_service = DatabaseService(config)
    try:
        # Initialize the database connection pool
        await db_service.initialize()
        app_logger.info("Database service initialized successfully")
    except Exception as e:
        app_logger.error("Failed to initialize database service", error=str(e))
        raise
    
    # Initialize background scraper service
    bg_scraper_service = BackgroundScraperService(db_service, config)
    
    # Initialize Langchain processor service
    langchain_processor_service = LangchainProcessorService(db_service, config)
    
    # Initialize LLM service
    llm_service = LLMService()
    
    # Initialize Langgraph service
    langgraph_service = LanggraphService()
    
    return db_service, bg_scraper_service, langchain_processor_service, llm_service, langgraph_service

async def start_background_services(bg_scraper_service, langchain_processor_service):
    """Start background services asynchronously"""
    app_logger.info("Starting background services")
    try:
        await bg_scraper_service.start()
        app_logger.info("Background scraper service started")
    except Exception as e:
        app_logger.error("Failed to start background scraper service", error=str(e))
        
    try:
        await langchain_processor_service.start()
        app_logger.info("Langchain processor service started")
    except Exception as e:
        app_logger.error("Failed to start langchain processor service", error=str(e))

async def stop_background_services(bg_scraper_service, langchain_processor_service):
    """Stop background services"""
    app_logger.info("Stopping background services")
    try:
        await bg_scraper_service.stop()
        app_logger.info("Background scraper service stopped")
    except Exception as e:
        app_logger.error("Error stopping background scraper service", error=str(e))
        
    try:
        await langchain_processor_service.stop()
        app_logger.info("Langchain processor service stopped")
    except Exception as e:
        app_logger.error("Error stopping langchain processor service", error=str(e))

def main():
    """Main application entry point."""
    app_logger.info("Starting Telegram Scraper Application")

    # Load configuration
    try:
        config = ConfigLoader.load_config()
        app_logger.info("Configuration loaded successfully")
    except Exception as e:
        app_logger.error("Failed to load configuration", error=str(e))
        return

    # Initialize all services
    try:
        db_service, bg_scraper_service, langchain_processor_service, llm_service, langgraph_service = asyncio.run(initialize_services(config))
    except Exception as e:
        app_logger.error("Failed to initialize services", error=str(e))
        return
    
    # Create lifespan manager for application lifecycle
    @asynccontextmanager
    async def lifespan(app):
        # Store services in app state
        app.state.db_service = db_service
        app.state.bg_scraper_service = bg_scraper_service
        app.state.langchain_processor_service = langchain_processor_service
        app.state.llm_service = llm_service
        app.state.langgraph_service = langgraph_service
        
        # Start background services
        asyncio.create_task(start_background_services(bg_scraper_service, langchain_processor_service))
        
        yield  # App is running
        
        # Shutdown: Stop background services
        await stop_background_services(bg_scraper_service, langchain_processor_service)
        
        # Close database service
        try:
            await db_service.close()
            app_logger.info("Database service closed successfully")
        except Exception as e:
            app_logger.error("Error closing database service", error=str(e))
    
    # Create FastAPI app with lifespan manager
    app = create_app(config, lifespan=lifespan)

    # Get server configuration
    server_config = config['server']
    host = server_config['host']
    port = server_config['port']

    app_logger.info("Starting API server on host", host=host, port=port)

    # Run the server
    uvicorn.run(
        app,
        host=host,
        port=port,
        log_level="info"
    )

if __name__ == "__main__":
    main()