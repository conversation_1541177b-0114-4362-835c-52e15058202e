"""initial_migration

Revision ID: a5f9268d03fa
Revises: 20250911_170000
Create Date: 2025-09-12 12:57:48.415347

"""
from alembic import op
import sqlalchemy as sa
import sys
sys.path.append('.')

# revision identifiers, used by Alembic.
revision = 'a5f9268d03fa'
down_revision = '20250911_170000'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_bot_state_project_id', table_name='bot_state')
    op.alter_column('messages', 'content',
               existing_type=mysql.LONGTEXT(),
               type_=sa.Text(),
               nullable=False)
    op.alter_column('messages', 'message',
               existing_type=mysql.LONGTEXT(),
               type_=sa.Text(),
               nullable=False)
    op.drop_index('ft_messages_content', table_name='messages', mysql_prefix='FULLTEXT')
    op.drop_index('ft_messages_message', table_name='messages', mysql_prefix='FULLTEXT')
    op.drop_index('ft_messages_tweets', table_name='messages', mysql_prefix='FULLTEXT')
    op.drop_index('idx_messages_created_at', table_name='messages')
    op.drop_index('idx_messages_entities_id', table_name='messages')
    op.drop_index('idx_messages_message_id', table_name='messages')
    op.drop_index('idx_messages_project_id', table_name='messages')
    op.drop_index('idx_otp_requests_created_at', table_name='otp_requests')
    op.drop_index('idx_otp_requests_expires_at', table_name='otp_requests')
    op.drop_index('idx_otp_requests_phone_number', table_name='otp_requests')
    op.alter_column('register', 'llm_api_key',
               existing_type=mysql.LONGTEXT(),
               type_=sa.Text(),
               nullable=False)
    op.alter_column('register', 'llm_system_prompt',
               existing_type=mysql.LONGTEXT(),
               type_=sa.Text(),
               nullable=False)
    op.alter_column('register', 'llm_user_prompt',
               existing_type=mysql.LONGTEXT(),
               type_=sa.Text(),
               nullable=False)
    op.drop_index('ft_llm_api_key', table_name='register', mysql_prefix='FULLTEXT')
    op.drop_index('ft_llm_system_prompt', table_name='register', mysql_prefix='FULLTEXT')
    op.drop_index('ft_llm_user_prompt', table_name='register', mysql_prefix='FULLTEXT')
    op.drop_index('idx_register_active', table_name='register')
    op.drop_index('idx_register_phone_number', table_name='register')
    op.drop_index('idx_register_project_id', table_name='register')
    op.alter_column('tg_content', 'content',
               existing_type=mysql.LONGTEXT(),
               type_=sa.Text(),
               nullable=False)
    op.drop_index('ft_content', table_name='tg_content', mysql_prefix='FULLTEXT')
    op.drop_index('idx_tg_content_entities_id', table_name='tg_content')
    op.drop_index('idx_tg_content_message_date', table_name='tg_content')
    op.drop_index('idx_tg_content_message_id', table_name='tg_content')
    op.drop_index('idx_tg_content_user_id', table_name='tg_content')
    op.drop_index('idx_tg_entities_name', table_name='tg_entities')
    op.drop_index('idx_tg_entities_tg_id', table_name='tg_entities')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_tg_entities_tg_id', 'tg_entities', ['tg_id'], unique=False)
    op.create_index('idx_tg_entities_name', 'tg_entities', ['name'], unique=False)
    op.create_index('idx_tg_content_user_id', 'tg_content', ['user_id'], unique=False)
    op.create_index('idx_tg_content_message_id', 'tg_content', ['message_id'], unique=False)
    op.create_index('idx_tg_content_message_date', 'tg_content', ['message_date'], unique=False)
    op.create_index('idx_tg_content_entities_id', 'tg_content', ['entities_id'], unique=False)
    op.create_index('ft_content', 'tg_content', ['content'], unique=False, mysql_prefix='FULLTEXT')
    op.alter_column('tg_content', 'content',
               existing_type=sa.Text(),
               type_=mysql.LONGTEXT(),
               nullable=True)
    op.create_index('idx_register_project_id', 'register', ['project_id'], unique=False)
    op.create_index('idx_register_phone_number', 'register', ['phone_number'], unique=False)
    op.create_index('idx_register_active', 'register', ['active'], unique=False)
    op.create_index('ft_llm_user_prompt', 'register', ['llm_user_prompt'], unique=False, mysql_prefix='FULLTEXT')
    op.create_index('ft_llm_system_prompt', 'register', ['llm_system_prompt'], unique=False, mysql_prefix='FULLTEXT')
    op.create_index('ft_llm_api_key', 'register', ['llm_api_key'], unique=False, mysql_prefix='FULLTEXT')
    op.alter_column('register', 'llm_user_prompt',
               existing_type=sa.Text(),
               type_=mysql.LONGTEXT(),
               nullable=True)
    op.alter_column('register', 'llm_system_prompt',
               existing_type=sa.Text(),
               type_=mysql.LONGTEXT(),
               nullable=True)
    op.alter_column('register', 'llm_api_key',
               existing_type=sa.Text(),
               type_=mysql.LONGTEXT(),
               nullable=True)
    op.create_index('idx_otp_requests_phone_number', 'otp_requests', ['phone_number'], unique=False)
    op.create_index('idx_otp_requests_expires_at', 'otp_requests', ['expires_at'], unique=False)
    op.create_index('idx_otp_requests_created_at', 'otp_requests', ['created_at'], unique=False)
    op.create_index('idx_messages_project_id', 'messages', ['project_id'], unique=False)
    op.create_index('idx_messages_message_id', 'messages', ['message_id'], unique=False)
    op.create_index('idx_messages_entities_id', 'messages', ['entities_id'], unique=False)
    op.create_index('idx_messages_created_at', 'messages', ['created_at'], unique=False)
    op.create_index('ft_messages_tweets', 'messages', ['message_used'], unique=False, mysql_prefix='FULLTEXT')
    op.create_index('ft_messages_message', 'messages', ['message'], unique=False, mysql_prefix='FULLTEXT')
    op.create_index('ft_messages_content', 'messages', ['content'], unique=False, mysql_prefix='FULLTEXT')
    op.alter_column('messages', 'message',
               existing_type=sa.Text(),
               type_=mysql.LONGTEXT(),
               nullable=True)
    op.alter_column('messages', 'content',
               existing_type=sa.Text(),
               type_=mysql.LONGTEXT(),
               nullable=True)
    op.create_index('idx_bot_state_project_id', 'bot_state', ['project_id'], unique=False)
    # ### end Alembic commands ###