"""
API helper functions for the Telegram Scraper application.
This module contains utility functions for API operations.
"""

from fastapi import HTTPException, status
from api.common import StandardResponse, StandardErrorResponse

def create_success_response(message: str, data: any = None, status_code: int = 200, **kwargs) -> StandardResponse:
    """
    Create a standardized success response.
    
    Args:
        message (str): Success message
        data (any): Response data
        status_code (int): HTTP status code
        **kwargs: Additional fields to include in the response for future extensibility
        
    Returns:
        StandardResponse: Standardized success response
        
    Example:
        >>> response = create_success_response("User created successfully", {"id": 1, "name": "<PERSON>"})
        >>> print(response.message)
        "User created successfully"
    """
    response_data = {
        "message": message,
        "error": False,
        "status": status_code,
        "data": data,
        **kwargs
    }
    return StandardResponse(**response_data)

def create_error_response(message: str, data: any = None, status_code: int = 400, **kwargs) -> StandardErrorResponse:
    """
    Create a standardized error response.
    
    Args:
        message (str): Error message
        data (any): Error data
        status_code (int): HTTP status code
        **kwargs: Additional fields to include in the response for future extensibility
        
    Returns:
        StandardErrorResponse: Standardized error response
        
    Example:
        >>> response = create_error_response("User not found", {"user_id": 1}, 404)
        >>> print(response.message)
        "User not found"
    """
    response_data = {
        "message": message,
        "error": True,
        "status": status_code,
        "data": data,
        **kwargs
    }
    return StandardErrorResponse(**response_data)

def raise_http_exception(message: str, status_code: int = 400, **kwargs) -> None:
    """
    Raise an HTTP exception with a standardized error response.
    
    Args:
        message (str): Error message
        status_code (int): HTTP status code
        **kwargs: Additional fields to include in the response for future extensibility
        
    Raises:
        HTTPException: HTTP exception with standardized error response
        
    Example:
        >>> raise_http_exception("Invalid input", 400)
        # Raises HTTPException with standardized error response
    """
    response_data = {
        "message": message,
        "error": True,
        "status": status_code,
        "data": "",  # Empty string for data field
        **kwargs
    }
    raise HTTPException(
        status_code=status_code,
        detail=StandardErrorResponse(**response_data).dict()
    )

def validate_phone_number(phone_number: str) -> bool:
    """
    Validate phone number format.
    
    Args:
        phone_number (str): Phone number to validate
        
    Returns:
        bool: True if valid, False otherwise
        
    Example:
        >>> validate_phone_number("+1234567890")
        True
        >>> validate_phone_number("1234567890")
        False
    """
    # Basic validation - phone number should start with + and contain only digits after that
    if not phone_number.startswith('+'):
        return False
    if not phone_number[1:].isdigit():
        return False
    return len(phone_number) >= 10  # Minimum length check

def validate_api_credentials(api_id: int, api_hash: str) -> bool:
    """
    Validate API credentials.
    
    Args:
        api_id (int): Telegram API ID
        api_hash (str): Telegram API hash
        
    Returns:
        bool: True if valid, False otherwise
        
    Example:
        >>> validate_api_credentials(1234567, "abcdef123456")
        True
        >>> validate_api_credentials(0, "abc")
        False
    """
    # Basic validation
    if not api_id or api_id <= 0:
        return False
    if not api_hash or len(api_hash) < 10:  # Basic length check
        return False
    return True