"""
Helper functions for tg_entity-related operations.
This module contains utility functions for handling tg_entities updates with real names and access_hash.
"""

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from models.tg_entity import TgEntity
from utils.logger import Logger

tg_entity_helper_logger = Logger("tg_entity_helper")

async def update_tg_entity_details(session: AsyncSession, tg_id: int, name: str, access_hash: str = None, entity_type: str = None, is_private: bool = None):
    """
    Update tg_entity with real name and access_hash from Telegram.
    
    Args:
        session (AsyncSession): Database session
        tg_id (int): Telegram internal ID
        name (str): Real name of the entity
        access_hash (str, optional): Access hash for the entity
        entity_type (str, optional): Type of entity (channel/group)
        is_private (bool, optional): Whether the entity is private
        
    Returns:
        TgEntity: Updated tg_entity object
    """
    try:
        # Check if entity already exists
        stmt = select(TgEntity).where(TgEntity.tg_id == str(tg_id))
        result = await session.execute(stmt)
        existing_entity = result.scalars().first()
        
        if existing_entity:
            # Update existing entity with real details
            existing_entity.name = name
            if access_hash:
                existing_entity.access_hash = access_hash
            if entity_type:
                existing_entity.type = entity_type
            if is_private is not None:
                existing_entity.is_private = 1 if is_private else 0
                
            tg_entity_helper_logger.info("Updated tg_entity with real name", tg_id=tg_id, name=name)
            return existing_entity
        else:
            # Create new entity if it doesn't exist
            new_entity = TgEntity(
                name=name,
                type=entity_type or "unknown",
                tg_id=str(tg_id),
                access_hash=access_hash,
                is_private=1 if is_private else 0 if is_private is not None else None
            )
            session.add(new_entity)
            await session.flush()  # Flush to get the ID
            tg_entity_helper_logger.info("Created new tg_entity with name", tg_id=tg_id, name=name)
            return new_entity
    except Exception as e:
        tg_entity_helper_logger.error("Failed to update tg_entity details", tg_id=tg_id, name=name, error=str(e))
        raise

async def get_or_create_tg_entity(session: AsyncSession, tg_id: int, name: str, access_hash: str = None, entity_type: str = None, is_private: bool = None):
    """
    Get existing tg_entity or create a new one with real details.
    
    Args:
        session (AsyncSession): Database session
        tg_id (int): Telegram internal ID
        name (str): Real name of the entity
        access_hash (str, optional): Access hash for the entity
        entity_type (str, optional): Type of entity (channel/group)
        is_private (bool, optional): Whether the entity is private
        
    Returns:
        TgEntity: tg_entity object
    """
    try:
        # Check if entity already exists
        stmt = select(TgEntity).where(TgEntity.tg_id == str(tg_id))
        result = await session.execute(stmt)
        existing_entity = result.scalars().first()
        
        if existing_entity:
            return existing_entity
        else:
            # Create new entity if it doesn't exist
            new_entity = TgEntity(
                name=name,
                type=entity_type or "unknown",
                tg_id=str(tg_id),
                access_hash=access_hash,
                is_private=1 if is_private else 0 if is_private is not None else None
            )
            session.add(new_entity)
            await session.flush()  # Flush to get the ID
            tg_entity_helper_logger.info("Created new tg_entity with name", tg_id=tg_id, name=name)
            return new_entity
    except Exception as e:
        tg_entity_helper_logger.error("Failed to get or create tg_entity", tg_id=tg_id, name=name, error=str(e))
        raise