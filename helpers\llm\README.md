# LLM (Large Language Model) Components

This directory contains the core implementation for interacting with Large Language Models using the LangChain framework. It provides a structured approach to working with different LLM providers and offers utility functions for common operations.

## Overview

The LLM components in this project provide a unified interface for working with different LLM providers (OpenAI, Anthropic) and offer specialized functionality for processing Telegram messages and performing crypto analysis.

## Components

### Core Module (`core.py`)

The core module contains the main LLM helper class:

- **LLMProvider**: Enum-like class for LLM providers (OpenAI, Anthropic)
- **LLMHelper**: Main class for LLM operations with methods for:
  - Initializing different LLM providers
  - Generating responses with system and user prompts
  - Handling message history
  - Processing Telegram-specific messages
  - Performing crypto analysis
  - Generating unique content
  - Ensuring complete responses with enhanced placeholder detection

### Factory Module (`factory.py`)

The factory module provides functions for creating LLM helpers based on configuration:

- **create_llm_helper()**: Creates LLM helpers based on model selection
- **create_llm_helper_from_register()**: Creates LLM helpers from register data
- **create_llm_helper_for_telegram()**: Creates Telegram-specific LLM helpers
- **create_llm_helper_for_crypto_analysis()**: Creates LLM helpers optimized for crypto analysis

### Model Selector Module (`model_selector.py`)

The model selector module provides functions for selecting appropriate models based on task requirements:

- **select_model_for_task()**: Selects the most appropriate model for a given task
- **get_model_capabilities()**: Returns capabilities of different models
- **validate_model_access()**: Checks if API keys are valid for selected models

### Utilities Module (`utils.py`)

The utilities module contains helper functions for common LLM operations:

- **JSON extraction** from LLM responses
- **Message formatting** for LLM input
- **Telegram context formatting**
- **Crypto data formatting**
- **Text truncation** to token limits
- **Response validation** with enhanced placeholder detection
- **Trading signal extraction**
- **Asset and confidence extraction**
- **Response completion** utilities

## Supported Providers

1. **OpenAI**: Supports GPT-3.5-turbo, GPT-4, and GPT-4-turbo
2. **Anthropic**: Supports Claude 3 models (Haiku, Sonnet, Opus)

## Usage

### Creating an LLM Helper

```python
from helpers.llm.factory import create_llm_helper

# Create an LLM helper (0-2 for OpenAI, 3-5 for Anthropic)
llm_helper = create_llm_helper(
    model_selection=1,  # GPT-4
    api_key="your-api-key",
    temperature=0.7,
    max_tokens=1000
)
```

### Generating a Response

```python
# Generate a simple response
response = await llm_helper.generate_response(
    system_prompt="You are a helpful assistant.",
    user_prompt="Explain quantum computing in simple terms."
)

# Generate a response with context
response = await llm_helper.generate_response(
    system_prompt="You are a crypto analyst.",
    user_prompt="Analyze the following market data: {market_data}",
    context={"market_data": "BTC: $50,000, ETH: $3,000"}
)
```

### Working with Message History

```python
messages = [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "What's the weather like?"},
    {"role": "assistant", "content": "I don't have access to real-time weather data."},
    {"role": "user", "content": "Can you explain how weather prediction works?"}
]

response = await llm_helper.generate_response_with_history(messages)
```

### Crypto Analysis

```python
market_data = {
    "symbol": "BTC",
    "price": "$50,000",
    "volume_24h": "$25B",
    "sentiment": "bullish"
}

analysis = await llm_helper.generate_crypto_analysis(market_data, "trading_signal")
```

### Unique Content Generation

```python
content = await llm_helper.generate_unique_content(
    topic="Blockchain Technology",
    content_type="article",
    style_guide={
        "tone": "informative",
        "length": "long-form",
        "audience": "general"
    }
)
```

## Specialized Functions

### Telegram-Specific Processing

The LLM components include specialized functions for processing Telegram messages, including support for message thread IDs and Telegram-specific context.

### Crypto Analysis

Specialized functions for analyzing cryptocurrency market data, extracting trading signals, and generating market insights.

### Content Generation

Functions for generating unique content on various topics with different styles and formats.

## Response Handling

The LLM components include utilities for:
- Validating responses with enhanced placeholder detection
- Extracting structured data (JSON) from responses
- Ensuring response completeness with improved error handling
- Counting tokens
- Handling errors gracefully

## Configuration

LLM helpers can be configured with:
- Provider and model selection
- Temperature settings
- Maximum token limits
- Timeout values
- API keys

## Integration

The LLM components integrate with:
- LangGraph workflows for complex processing
- Database services for data persistence
- Configuration loading for settings management
- Logging for monitoring and debugging

## Debugging Common Errors

### 1. API Key Errors
- **Symptom**: `AuthenticationError` or `PermissionError`
- **Cause**: Invalid or missing API keys
- **Solution**:
  - Verify API keys are correctly set in the register table
  - Check that API keys have the necessary permissions
  - Ensure API keys are not expired

### 2. Model Access Errors
- **Symptom**: `RateLimitError` or `ModelNotFoundError`
- **Cause**: Exceeded rate limits or using unavailable models
- **Solution**:
  - Check rate limit status on provider dashboards
  - Verify model names are correct and available
  - Consider switching to alternative models

### 3. Timeout Errors
- **Symptom**: `TimeoutError` or no response after extended time
- **Cause**: Slow model response or network issues
- **Solution**:
  - Increase timeout values in configuration
  - Check network connectivity
  - Retry the request with exponential backoff

### 4. Context Length Errors
- **Symptom**: `ContextLengthExceededError`
- **Cause**: Input prompts exceeding model context limits
- **Solution**:
  - Use text truncation utilities to reduce input size
  - Summarize long contexts before sending to LLM
  - Consider using models with larger context windows

### 5. Response Validation Failures
- **Symptom**: Empty or placeholder responses
- **Cause**: Low-quality responses filtered by validation
- **Solution**:
  - Adjust temperature settings for more consistent responses
  - Improve prompts with clearer instructions
  - Check for placeholder content patterns in responses

### 6. Token Limit Issues
- **Symptom**: Incomplete responses or `TokenLimitError`
- **Cause**: Responses exceeding specified token limits
- **Solution**:
  - Increase max_tokens parameter
  - Use response truncation utilities
  - Implement response chunking for long outputs

## Logging and Monitoring

All LLM operations are logged using the project's logging system:
- INFO level: Successful operations and response statistics
- DEBUG level: Detailed prompt and response information
- ERROR level: API errors, validation failures, and exceptions

To enable debug logging, set the log level to DEBUG in the configuration.

## Recent Enhancements

### Enhanced Content Quality
- Improved response validation to detect and filter placeholder content
- Added checks for common placeholder patterns (e.g., "XXX", "placeholder")
- Enhanced completeness checking to ensure meaningful responses
- Multi-level validation across core, service, and utility modules

### Better Error Handling
- More robust error detection and handling
- Improved logging for debugging purposes
- Graceful degradation when LLM responses are unsatisfactory
- Enhanced validation of response quality

### Performance Improvements
- Optimized token counting for better performance
- Enhanced caching mechanisms where appropriate
- Reduced unnecessary processing of low-quality content
- Improved response truncation and completion algorithms

### Crypto Analysis Enhancements
- Enhanced crypto-related message detection with keyword and hashtag filtering
- Improved trading signal extraction from LLM responses
- Better asset and confidence level extraction
- Specialized prompts for different types of crypto analysis

### Telegram Integration Improvements
- Enhanced context handling for Telegram messages
- Better support for message thread IDs and forum topics
- Improved reply-to message processing with original message retrieval
- Enhanced formatting for Telegram-specific data

## Project Integration

This component is part of the larger Telegram Scraper project. For more information about the overall architecture and how this component fits in, see the [main project README](../../README.md).