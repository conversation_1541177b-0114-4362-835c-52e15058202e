from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Integer, String, ForeignKey, DateTime
from sqlalchemy.orm import Mapped, mapped_column
from models.base import Base

class OtpRequest(Base):
    __tablename__ = 'otp_requests'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    phone_number: Mapped[str] = mapped_column(String(255), nullable=False)
    otp_code_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    api_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    api_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    created_at: Mapped[int] = mapped_column(BigInteger, nullable=False)
    expires_at: Mapped[int] = mapped_column(BigInteger, nullable=False)
    is_verified: Mapped[int] = mapped_column(Integer, nullable=False, default=0)  # TINYINT