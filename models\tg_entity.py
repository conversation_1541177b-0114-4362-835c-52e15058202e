from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Inte<PERSON>, String, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from models.base import Base

class TgEntity(Base):
    __tablename__ = 'tg_entities'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    type: Mapped[str] = mapped_column(String(255), nullable=False)  # public/private
    tg_id: Mapped[str] = mapped_column(String(255), nullable=False)  # tg internal id for the channel/group
    access_hash: Mapped[str] = mapped_column(String(255), nullable=True)
    is_private: Mapped[int] = mapped_column(Integer, nullable=True)  # TINYINT

    # Relationships
    tg_contents: Mapped[list["TgContent"]] = relationship("TgContent", back_populates="entity")
    messages: Mapped[list["Message"]] = relationship("Message", back_populates="entity")