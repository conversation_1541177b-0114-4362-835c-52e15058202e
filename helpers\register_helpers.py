"""
Helper functions for register-related operations.
This module contains utility functions for handling register and tg_entities relationships.
"""

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from models.register_tg_entity import RegisterTgEntity
from models.tg_entity import TgEntity
from models.register import Register
from utils.logger import Logger

register_helper_logger = Logger("register_helper")

async def get_register_tg_entities(session: AsyncSession, project_id: int) -> list:
    """
    Get all tg_entities associated with a project ID.
    
    Args:
        session (AsyncSession): Database session
        project_id (int): Project ID from the register table
        
    Returns:
        list: List of tg_entity objects associated with the project
    """
    try:
        stmt = select(TgEntity).join(
            RegisterTgEntity, TgEntity.id == RegisterTgEntity.tg_entity_id
        ).where(RegisterTgEntity.project_id == project_id)
        
        result = await session.execute(stmt)
        entities = result.scalars().all()
        return entities
    except Exception as e:
        register_helper_logger.error("Failed to get project tg_entities for project_id", project_id=project_id, error=str(e))
        raise

async def get_register_tg_entities_with_thread_info(session: AsyncSession, project_id: int) -> list:
    """
    Get all tg_entities associated with a project ID, including message_thread_id information.
    
    Args:
        session (AsyncSession): Database session
        project_id (int): Project ID from the register table
        
    Returns:
        list: List of dictionaries containing tg_entity objects and their message_thread_id
    """
    try:
        stmt = select(TgEntity, RegisterTgEntity.message_thread_id).join(
            RegisterTgEntity, TgEntity.id == RegisterTgEntity.tg_entity_id
        ).where(RegisterTgEntity.project_id == project_id)
        
        result = await session.execute(stmt)
        entities_with_thread_info = []
        for row in result.fetchall():
            entity, thread_id = row
            entities_with_thread_info.append({
                'entity': entity,
                'message_thread_id': thread_id
            })
        return entities_with_thread_info
    except Exception as e:
        register_helper_logger.error("Failed to get project tg_entities with thread info for project_id", project_id=project_id, error=str(e))
        raise

async def get_register_tg_entities_names(session: AsyncSession, project_id: int) -> list:
    """
    Get all tg_entity names associated with a project ID.
    
    Args:
        session (AsyncSession): Database session
        project_id (int): Project ID from the register table
        
    Returns:
        list: List of tg_entity names associated with the project
    """
    try:
        stmt = select(TgEntity.name).join(
            RegisterTgEntity, TgEntity.id == RegisterTgEntity.tg_entity_id
        ).where(RegisterTgEntity.project_id == project_id)
        
        result = await session.execute(stmt)
        entity_names = [row[0] for row in result.fetchall()]
        return entity_names
    except Exception as e:
        register_helper_logger.error("Failed to get project tg_entities names for project_id", project_id=project_id, error=str(e))
        raise

async def get_active_registers_for_entity(session: AsyncSession, tg_entity_id: int) -> list:
    """
    Get all active register entries associated with a tg_entity ID.
    
    Args:
        session (AsyncSession): Database session
        tg_entity_id (int): ID of the tg_entity
        
    Returns:
        list: List of active Register objects associated with the tg_entity
    """
    try:
        stmt = select(Register).join(
            RegisterTgEntity, Register.project_id == RegisterTgEntity.project_id
        ).where(
            RegisterTgEntity.tg_entity_id == tg_entity_id,
            Register.active == 1
        )
        
        result = await session.execute(stmt)
        registers = result.scalars().all()
        return registers
    except Exception as e:
        register_helper_logger.error("Failed to get active registers for entity tg_entity_id", tg_entity_id=tg_entity_id, error=str(e))
        raise

async def get_active_registers_for_entity_with_thread_info(session: AsyncSession, tg_entity_id: int) -> list:
    """
    Get all active register entries associated with a tg_entity ID, including message_thread_id information.
    
    Args:
        session (AsyncSession): Database session
        tg_entity_id (int): ID of the tg_entity
        
    Returns:
        list: List of dictionaries containing active Register objects and their message_thread_id
    """
    try:
        stmt = select(Register, RegisterTgEntity.message_thread_id).join(
            RegisterTgEntity, Register.project_id == RegisterTgEntity.project_id
        ).where(
            RegisterTgEntity.tg_entity_id == tg_entity_id,
            Register.active == 1
        )
        
        result = await session.execute(stmt)
        registers_with_thread_info = []
        for row in result.fetchall():
            register, thread_id = row
            registers_with_thread_info.append({
                'register': register,
                'message_thread_id': thread_id
            })
        return registers_with_thread_info
    except Exception as e:
        register_helper_logger.error("Failed to get active registers for entity with thread info tg_entity_id", tg_entity_id=tg_entity_id, error=str(e))
        raise

async def get_all_active_tg_entities_with_registers(session: AsyncSession) -> dict:
    """
    Get all active tg_entities with their associated active registers.
    
    Args:
        session (AsyncSession): Database session
        
    Returns:
        dict: Dictionary mapping tg_entity IDs to lists of active Register objects
    """
    try:
        # Get all active registers
        register_stmt = select(Register).where(Register.active == 1)
        register_result = await session.execute(register_stmt)
        active_registers = {reg.project_id: reg for reg in register_result.scalars().all()}
        
        # Get all register-tg_entity relationships
        relationship_stmt = select(RegisterTgEntity)
        relationship_result = await session.execute(relationship_stmt)
        relationships = relationship_result.scalars().all()
        
        # Group active registers by tg_entity_id
        entity_registers = {}
        for relationship in relationships:
            # Check if the register is active
            if relationship.project_id in active_registers:
                if relationship.tg_entity_id not in entity_registers:
                    entity_registers[relationship.tg_entity_id] = []
                entity_registers[relationship.tg_entity_id].append(
                    active_registers[relationship.project_id]
                )
        
        # Get all tg_entities
        entity_stmt = select(TgEntity)
        entity_result = await session.execute(entity_stmt)
        all_entities = entity_result.scalars().all()
        
        # Filter to only include entities that have active registers
        result = {
            entity.id: entity_registers.get(entity.id, []) 
            for entity in all_entities 
            if entity.id in entity_registers
        }
        
        return result
    except Exception as e:
        register_helper_logger.error("Failed to get active tg_entities with registers", error=str(e))
        raise

async def get_all_active_tg_entities_with_registers_and_threads(session: AsyncSession) -> dict:
    """
    Get all active tg_entities with their associated active registers and message_thread_id information.
    
    Args:
        session (AsyncSession): Database session
        
    Returns:
        dict: Dictionary mapping tg_entity IDs to lists of dictionaries containing 
              active Register objects and their message_thread_id
    """
    try:
        # Get all register-tg_entity relationships with thread info
        relationship_stmt = select(RegisterTgEntity)
        relationship_result = await session.execute(relationship_stmt)
        relationships = relationship_result.scalars().all()
        
        # Get all active registers
        register_stmt = select(Register).where(Register.active == 1)
        register_result = await session.execute(register_stmt)
        active_registers = {reg.project_id: reg for reg in register_result.scalars().all()}
        
        # Group active registers by tg_entity_id with thread info
        entity_registers = {}
        for relationship in relationships:
            # Check if the register is active
            if relationship.project_id in active_registers:
                if relationship.tg_entity_id not in entity_registers:
                    entity_registers[relationship.tg_entity_id] = []
                entity_registers[relationship.tg_entity_id].append({
                    'register': active_registers[relationship.project_id],
                    'message_thread_id': relationship.message_thread_id
                })
        
        # Get all tg_entities
        entity_stmt = select(TgEntity)
        entity_result = await session.execute(entity_stmt)
        all_entities = entity_result.scalars().all()
        
        # Filter to only include entities that have active registers
        result = {
            entity.id: entity_registers.get(entity.id, []) 
            for entity in all_entities 
            if entity.id in entity_registers
        }
        
        return result
    except Exception as e:
        register_helper_logger.error("Failed to get active tg_entities with registers and threads", error=str(e))
        raise

async def get_register_by_project_id(session: AsyncSession, project_id: int) -> Register:
    """
    Get register entry by project ID.
    
    Args:
        session (AsyncSession): Database session
        project_id (int): Project ID
        
    Returns:
        Register: Register object
    """
    try:
        stmt = select(Register).where(Register.project_id == project_id)
        result = await session.execute(stmt)
        register = result.scalars().first()
        return register
    except Exception as e:
        register_helper_logger.error("Failed to get register by project ID", project_id=project_id, error=str(e))
        raise

async def get_message_thread_id_for_register_tg_entity(session: AsyncSession, project_id: int, tg_entity_id: int) -> int:
    """
    Get message_thread_id for a specific register-tg_entity relationship.
    
    Args:
        session (AsyncSession): Database session
        project_id (int): Project ID
        tg_entity_id (int): Telegram entity ID
        
    Returns:
        int: message_thread_id or None if not found
    """
    try:
        stmt = select(RegisterTgEntity.message_thread_id).where(
            RegisterTgEntity.project_id == project_id,
            RegisterTgEntity.tg_entity_id == tg_entity_id
        )
        result = await session.execute(stmt)
        thread_id = result.scalar()
        return thread_id
    except Exception as e:
        register_helper_logger.error("Failed to get message_thread_id for register-tg_entity relationship", project_id=project_id, tg_entity_id=tg_entity_id, error=str(e))
        raise