from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, String, Text, func, Integer, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship
from models.base import Base

class Register(Base):
    __tablename__ = 'register'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    project_id: Mapped[int] = mapped_column(Integer, nullable=False, unique=True)  # Project ID column positioned after id
    phone_number: Mapped[str] = mapped_column(String(255), nullable=False)
    api_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    api_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    tg_bot_token: Mapped[str] = mapped_column(String(255), nullable=False)
    tg_chat_id: Mapped[str] = mapped_column(String(255), nullable=True)
    message_thread_id: Mapped[int] = mapped_column(BigInteger, nullable=True)  # Message thread ID for subtopics
    scheduler_to_post: Mapped[int] = mapped_column(Integer, nullable=False)
    active_post_hour_start: Mapped[int] = mapped_column(Integer, nullable=False)
    active_post_hour_end: Mapped[int] = mapped_column(Integer, nullable=False)
    active_reply_hour_start: Mapped[int] = mapped_column(Integer, nullable=False)
    active_reply_hour_end: Mapped[int] = mapped_column(Integer, nullable=False)
    keyword: Mapped[str] = mapped_column(String(255), nullable=True)
    hashtag: Mapped[str] = mapped_column(String(255), nullable=True)
    model_selection: Mapped[int] = mapped_column(Integer, nullable=False)
    llm_api_key: Mapped[str] = mapped_column(Text, nullable=False)
    llm_max_token: Mapped[int] = mapped_column(Integer, nullable=False)
    llm_temperature: Mapped[float] = mapped_column(Float, nullable=False)
    llm_top_p: Mapped[float] = mapped_column(Float, nullable=False)
    llm_system_prompt: Mapped[str] = mapped_column(Text, nullable=False)
    llm_user_prompt: Mapped[str] = mapped_column(Text, nullable=False)
    reply_mentions: Mapped[int] = mapped_column(Integer, nullable=False)  # TINYINT
    expiry_date: Mapped[int] = mapped_column(BigInteger, nullable=False)
    active: Mapped[int] = mapped_column(Integer, nullable=False, default=0)  # TINYINT with default
    is_verify: Mapped[int] = mapped_column(Integer, nullable=False)  # TINYINT
    created_at: Mapped[int] = mapped_column(BigInteger, nullable=False)