from fastapi import APIRout<PERSON>, Depends, HTTPException, status, Header, Request
from fastapi.security import <PERSON>Auth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from pydantic import BaseModel
from typing import Optional
from utils.logger import Logger
from services.auth import AuthService
from helpers.api_helpers import (
    create_success_response, 
    create_error_response, 
    raise_http_exception
)
from helpers.validation import (
    body, 
    validate_data, 
    raise_validation_error
)
from services.database import DatabaseService
from models.register import Register
from models.otp_request import OtpRequest
from models.tg_entity import TgEntity
from helpers.tg_entity_helpers import update_tg_entity_details
import time
import random
import string

router = APIRouter()
auth_logger = Logger("api.auth")

# Pydantic models for response only
class Token(BaseModel):
    access_token: str
    token_type: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer"
            }
        }

class SessionCheckResponse(BaseModel):
    exists: bool
    
    class Config:
        json_schema_extra = {
            "example": {
                "exists": True
            }
        }

class OtpRequestResponse(BaseModel):
    otp_session_id: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "otp_session_id": "abc123def456..."
            }
        }

class ChannelResponse(BaseModel):
    id: int
    name: str
    username: Optional[str] = None
    type: str
    is_private: bool
    subtopics: list
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 123456789,
                "name": "Crypto News Group",
                "username": "cryptonews",
                "type": "group",
                "is_private": False,
                "subtopics": [
                    {
                        "id": 1,
                        "title": "General Discussion",
                        "creator_id": 987654321,
                        "date": 1700000000
                    }
                ]
            }
        }

# Security dependency
def verify_bearer_token(authorization: str = Header(...)):
    """Verify bearer token from header."""
    if not authorization.startswith("Bearer "):
        raise_http_exception("Invalid authorization header", 401)
    token = authorization[7:]  # Remove "Bearer " prefix
    # Verify the token against config
    from utils.config_loader import ConfigLoader
    config = ConfigLoader.load_config()
    if token != config["security"]["console_bearer_token"]:
        raise_http_exception("Invalid token", 401)
    return token

@router.post("/check-session",
             summary="Check if a session file exists",
             description="Check if a session file exists for the given project ID. Requires authentication with a bearer token.",
             response_description="Session existence status")
async def check_session(request: Request, token: str = Depends(verify_bearer_token)):
    """Check if a session file exists for the given project ID."""
    # Parse JSON body
    try:
        body_data = await request.json()
    except:
        # Create error response with empty data field
        error_response = create_error_response(
            "Invalid JSON body",
            "",
            400
        )
        return error_response.dict()
    
    # Set up validation chains
    chains = body(["project_id"])
    chains["project_id"].is_required().is_int().custom(lambda x: int(x) > 0 if str(x).strip() else False, "Project ID must be a positive integer")
    
    # Validate data
    result = validate_data(body_data, chains)
    if not result.is_valid():
        # Create error response with empty data field but with validation message
        error_messages = [error.message for error in result.errors]
        error_message = "; ".join(error_messages) if error_messages else "Validation failed"
        
        error_response = create_error_response(
            error_message,
            "",
            400
        )
        return error_response.dict()
    
    project_id = int(body_data["project_id"])
    auth_logger.info("Session check request for project_id", project_id=project_id)
    
    # Get phone number from register table using project_id
    db_service = request.app.state.db_service
    session = await db_service.get_session()
    try:
        from sqlalchemy import select
        from models.register import Register
        
        # Get user's phone number from register table using project_id
        stmt = select(Register).where(Register.project_id == project_id)
        result = await session.execute(stmt)
        user = result.scalars().first()
        
        if not user:
            # Create error response with empty data field
            error_response = create_error_response(
                "Project not found",
                "",
                404
            )
            return error_response.dict()
        
        phone_number = user.phone_number
        auth_logger.info("Found user phone number for project_id", project_id=project_id, phone_number=phone_number)
    finally:
        await session.close()
    
    # Check if session file exists (Telethon adds .session extension automatically)
    import os
    session_path = f'sessions/session_{phone_number}'
    session_file_path = f'{session_path}.session'  # Telethon adds .session extension
    
    if os.path.exists(session_file_path):
        return create_success_response(
            "Session file exists",
            {"exists": True}
        ).dict()
    else:
        return create_success_response(
            "Session file does not exist",
            {"exists": False}
        ).dict()

@router.post("/request-otp",
             summary="Request OTP code",
             description="Request OTP code to be sent to user's phone number. Requires authentication with a bearer token.",
             response_description="OTP session ID")
async def request_otp(request: Request, token: str = Depends(verify_bearer_token)):
    """Request OTP code to be sent to user's phone number."""
    # Parse JSON body
    try:
        body_data = await request.json()
    except:
        raise_http_exception("Invalid JSON body", 400)
    
    # Set up validation chains
    chains = body(["phone_number", "api_id", "api_hash"])
    chains["phone_number"].is_required().is_phone_number()
    chains["api_id"].is_required().is_int().custom(lambda x: int(x) > 0 if str(x).strip() else False, "API ID must be a positive integer")
    chains["api_hash"].is_required().is_string().is_length(min_length=10)
    
    # Validate data
    result = validate_data(body_data, chains)
    if not result.is_valid():
        raise_validation_error(result)
    
    phone_number = body_data["phone_number"]
    api_id = int(body_data["api_id"])
    api_hash = body_data["api_hash"]
    
    auth_logger.info("OTP request for phone_number", phone_number=phone_number)
    
    # Check for existing unexpired OTP requests (spam prevention)
    db_service = request.app.state.db_service
    session = await db_service.get_session()
    try:
        from sqlalchemy import select
        # Check for existing unexpired OTP requests for the same phone number
        current_time = int(time.time())
        stmt = select(OtpRequest).where(
            OtpRequest.phone_number == phone_number,
            OtpRequest.expires_at > current_time,
            OtpRequest.is_verified == 0  # Not yet verified
        )
        result = await session.execute(stmt)
        existing_request = result.scalars().first()
        
        if existing_request:
            # Check if it's been less than 5 minutes since the last request
            time_since_last_request = current_time - existing_request.created_at
            if time_since_last_request < 300:  # 5 minutes
                raise_http_exception("OTP request limit exceeded. Please wait before requesting a new OTP.", 429)
        
        # Generate OTP code hash (similar to Telegram's phone_code_hash)
        otp_code_hash = ''.join(random.choices(string.ascii_letters + string.digits, k=32))
        
        # Store OTP request in database
        current_time = int(time.time())
        expiry_time = current_time + 300  # OTP expires in 5 minutes
        
        otp_request = OtpRequest(
            phone_number=phone_number,
            otp_code_hash=otp_code_hash,
            api_id=api_id,
            api_hash=api_hash,
            created_at=current_time,
            expires_at=expiry_time,
            is_verified=0
        )
        
        session.add(otp_request)
        await session.commit()
        await session.refresh(otp_request)
        
    except Exception as e:
        await session.rollback()
        auth_logger.error("Error creating OTP request", error=str(e))
        raise
    finally:
        await session.close()
    
    # Use Telethon to send OTP
    from telethon import TelegramClient
    client = TelegramClient(f'sessions/session_{phone_number}', api_id, api_hash)
    try:
        await client.connect()
        sent = await client.send_code_request(phone_number)
        await client.disconnect()
        otp_session_id = sent.phone_code_hash
        
        # Update the OTP request with the actual Telegram phone_code_hash
        session = await db_service.get_session()
        try:
            otp_request.otp_code_hash = otp_session_id
            session.add(otp_request)
            await session.commit()
        except Exception as e:
            await session.rollback()
            auth_logger.error("Error updating OTP request with Telegram code hash", error=str(e))
            raise
        finally:
            await session.close()
        
        return create_success_response(
            "OTP sent successfully",
            {"otp_session_id": otp_session_id}
        ).dict()
    except Exception as e:
        auth_logger.error("Failed to send OTP", phone_number=phone_number, error=str(e))
        raise_http_exception(f"Failed to send OTP: {str(e)}", 500)

@router.post("/verify-otp",
             summary="Verify OTP code",
             description="Verify OTP code sent to user's phone number. Requires authentication with a bearer token.",
             response_description="Access token")
async def verify_otp(request: Request, token: str = Depends(verify_bearer_token)):
    """Verify OTP code."""
    # Parse JSON body
    try:
        body_data = await request.json()
    except:
        raise_http_exception("Invalid JSON body", 400)
    
    # Set up validation chains
    chains = body(["phone_number", "otp_code"])
    chains["phone_number"].is_required().is_phone_number()
    chains["otp_code"].is_required().is_string().is_length(min_length=5, max_length=5).matches(r'^\d{5}$', "OTP code must be 5 digits")
    
    # Validate data
    result = validate_data(body_data, chains)
    if not result.is_valid():
        raise_validation_error(result)
    
    phone_number = body_data["phone_number"]
    otp_code = body_data["otp_code"]
    
    auth_logger.info("OTP verification for phone_number", phone_number=phone_number)
    
    # Get user's API credentials from OTP request only (not from register table)
    db_service = request.app.state.db_service
    session = await db_service.get_session()
    try:
        # Use select statement with execute instead of query
        from sqlalchemy import select
        # Get the OTP request from the database
        stmt = select(OtpRequest).where(OtpRequest.phone_number == phone_number).order_by(OtpRequest.created_at.desc())
        result = await session.execute(stmt)
        otp_request = result.scalars().first()
        
        if not otp_request:
            raise_http_exception("OTP request not found. Please request OTP first.", 400)
        
        # Check if OTP has expired
        current_time = int(time.time())
        if current_time > otp_request.expires_at:
            raise_http_exception("OTP has expired. Please request a new OTP.", 400)
        
        # Check if OTP is already verified
        if otp_request.is_verified == 1:
            raise_http_exception("OTP already verified.", 400)
        
        otp_session_id = otp_request.otp_code_hash
        api_id = otp_request.api_id
        api_hash = otp_request.api_hash
        
        if not otp_session_id:
            raise_http_exception("OTP session not found. Please request OTP first.", 400)
            
        # Update OTP request as verified in database
        otp_request.is_verified = 1
        session.add(otp_request)
        await session.commit()
    except Exception as e:
        await session.rollback()
        auth_logger.error("Error verifying OTP request", error=str(e))
        raise
    finally:
        await session.close()
    
    # Verify the OTP with Telegram (no database record needed for verification result as per requirements)
    from telethon import TelegramClient
    client = TelegramClient(f'sessions/session_{phone_number}', api_id, api_hash)
    try:
        await client.connect()
        # Sign in with the provided OTP
        await client.sign_in(phone_number, otp_code, phone_code_hash=otp_session_id)
        await client.disconnect()
        
        # Create a token for the user (no database record needed for verification result)
        access_token = AuthService.create_access_token(data={"sub": phone_number})
        return create_success_response(
            "OTP verified successfully",
            {"access_token": access_token, "token_type": "bearer"}
        ).dict()
    except Exception as e:
        auth_logger.error("Failed to verify OTP", phone_number=phone_number, error=str(e))
        raise_http_exception(f"Failed to verify OTP: {str(e)}", 401)

@router.post("/channels",
             summary="Get all channels/groups",
             description="Get all channels/groups for the authenticated user. Requires authentication with a bearer token.",
             response_description="List of channels and groups")
async def get_channels(request: Request, token: str = Depends(verify_bearer_token)):
    """Get all channels/groups for the authenticated user."""
    # Parse JSON body
    try:
        body_data = await request.json()
    except:
        raise_http_exception("Invalid JSON body", 400)
    
    # Set up validation chains
    chains = body(["phone_number", "api_id", "api_hash"])
    chains["phone_number"].is_required().is_phone_number()
    chains["api_id"].is_required().is_int().custom(lambda x: int(x) > 0 if str(x).strip() else False, "API ID must be a positive integer")
    chains["api_hash"].is_required().is_string().is_length(min_length=10)
    
    # Validate data
    result = validate_data(body_data, chains)
    if not result.is_valid():
        raise_validation_error(result)
    
    phone_number = body_data["phone_number"]
    api_id = int(body_data["api_id"])
    api_hash = body_data["api_hash"]
    
    auth_logger.info("Channel request with credentials", phone_number=phone_number, api_id=api_id)
    
    # Verify that the user has a verified OTP request
    db_service = request.app.state.db_service
    session = await db_service.get_session()
    try:
        from sqlalchemy import select
        from models.otp_request import OtpRequest
        # Get the last OTP request for this phone number
        stmt = select(OtpRequest).where(
            OtpRequest.phone_number == phone_number
        ).order_by(OtpRequest.created_at.desc()).limit(1)
        result = await session.execute(stmt)
        otp_request = result.scalars().first()
        
        # Check if there's a verified OTP request
        if not otp_request or otp_request.is_verified != 1:
            raise_http_exception("Session not verified. Please verify OTP first.", 401)
        
        # Verify that the API credentials match
        if otp_request.api_id != api_id or otp_request.api_hash != api_hash:
            raise_http_exception("API credentials do not match the verified session.", 401)
        
        auth_logger.info("Verified session found for phone_number", phone_number=phone_number)
    finally:
        await session.close()
    
    auth_logger.info("Fetching channels for phone_number", phone_number=phone_number)
    
    # Check if session file exists (Telethon adds .session extension automatically)
    import os
    session_path = f'sessions/session_{phone_number}'
    session_file_path = f'{session_path}.session'  # Telethon adds .session extension
    auth_logger.info("Checking session file", session_path=session_path, session_file_path=session_file_path)
    
    if not os.path.exists(session_file_path):
        # List all files in sessions directory for debugging
        sessions_dir = 'sessions'
        if os.path.exists(sessions_dir):
            files = os.listdir(sessions_dir)
            auth_logger.error("Session file not found", expected_path=session_file_path, actual_session_path=session_path, files_in_sessions_dir=files)
        else:
            auth_logger.error("Sessions directory does not exist", directory=sessions_dir)
        raise_http_exception("Session not found. Please verify OTP first.", 400)
    
    # Fetch channels using Telethon
    from telethon import TelegramClient
    from telethon.tl.types import Channel, Chat, User
    from telethon.tl.functions.channels import GetForumTopicsRequest
    client = TelegramClient(session_path, api_id, api_hash)  # Pass session_path without .session extension
    try:
        await client.connect()
        if not await client.is_user_authorized():
            raise_http_exception("Session not authorized. Please verify OTP first.", 401)
        
        # Get all dialogs (channels and groups)
        dialogs = await client.get_dialogs()
        channels = []
        
        # Get database session for updating tg_entities
        db_service = request.app.state.db_service
        session = await db_service.get_session()
        
        try:
            for dialog in dialogs:
                # Skip private conversations (User entities)
                if isinstance(dialog.entity, User):
                    continue
                    
                # Process channels and groups (both public and private)
                # Determine type based on entity type
                if isinstance(dialog.entity, Channel):
                    # Check if it's a megagroup (supergroup) or regular channel
                    entity_type = "group" if getattr(dialog.entity, 'megagroup', False) else "channel"
                elif isinstance(dialog.entity, Chat):
                    # Regular chat (legacy groups)
                    entity_type = "group"
                else:
                    # Default to channel for other types
                    entity_type = "channel"
                
                # Get access_hash if available
                access_hash = getattr(dialog.entity, 'access_hash', None)
                
                # Update or create tg_entity in database with real details
                try:
                    await update_tg_entity_details(
                        session,
                        dialog.entity.id,
                        dialog.name,
                        str(access_hash) if access_hash else None,
                        entity_type,
                        getattr(dialog.entity, 'username', None) is None
                    )
                    await session.commit()
                except Exception as e:
                    auth_logger.error("Failed to update tg_entity in database", entity_id=dialog.entity.id, entity_name=dialog.name, error=str(e))
                    # Continue with the process even if database update fails
                
                # Create channel object using Telegram's internal ID
                # For private channels/groups, username will be None
                channel_obj = {
                    "id": dialog.entity.id,  # Telegram Internal ID
                    "name": dialog.name,
                    "username": getattr(dialog.entity, 'username', None),  # Could be None for private channels
                    "type": entity_type,
                    "is_private": getattr(dialog.entity, 'username', None) is None  # Private if no username
                }
                
                # Check for subtopics (forums) and fetch them if available
                if hasattr(dialog.entity, 'forum') and dialog.entity.forum:
                    try:
                        # Fetch forum topics using the correct Telethon method
                        topics_result = await client(GetForumTopicsRequest(
                            channel=dialog.entity,
                            offset_date=0,
                            offset_id=0,
                            offset_topic=0,
                            limit=100  # Adjust limit as needed
                        ))
                        subtopics = []
                        if hasattr(topics_result, 'topics'):
                            for topic in topics_result.topics:
                                # Convert date to Unix epoch time if it exists
                                topic_date = None
                                if hasattr(topic, 'date') and topic.date:
                                    # Convert Telethon's datetime to Unix timestamp
                                    if hasattr(topic.date, 'timestamp'):
                                        topic_date = int(topic.date.timestamp())
                                
                                subtopics.append({
                                    "id": topic.id,
                                    "title": topic.title,
                                    "creator_id": getattr(topic, 'creator_id', None),
                                    "date": topic_date  # Unix epoch time
                                })
                        channel_obj["subtopics"] = subtopics
                    except Exception as e:
                        # If we can't fetch subtopics, just set an empty array
                        auth_logger.warning("Failed to fetch subtopics for channel_id", channel_id=dialog.entity.id, error=str(e))
                        channel_obj["subtopics"] = []
                else:
                    # No subtopics available
                    channel_obj["subtopics"] = []
                
                channels.append(channel_obj)
        finally:
            await session.close()
            
        await client.disconnect()
        
        # Return standard response format
        return create_success_response(
            "Channels retrieved successfully",
            channels
        ).dict()
    except Exception as e:
        auth_logger.error("Failed to fetch channels", phone_number=phone_number, error=str(e))
        raise_http_exception(f"Failed to fetch channels: {str(e)}", 500)
