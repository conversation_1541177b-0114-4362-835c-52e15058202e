"""
LLM service for processing messages with Large Language Models.
"""

import asyncio
import re
import traceback
from typing import Dict, Any, Optional, List
from utils.logger import Logger
from utils.config_loader import ConfigLoader
from helpers.llm.factory import create_llm_helper_from_register, create_llm_helper_for_crypto_analysis
from helpers.llm.utils import validate_llm_response, extract_json_from_response, extract_trading_signals_from_response
from helpers.validation_helpers import detect_fake_content, validate_trading_signal_content, is_content_sufficiently_detailed

# Initialize logger
llm_service_logger = Logger("llm_service")

class LLMService:
    """Service for LLM operations"""
    
    def __init__(self):
        """Initialize the LLM service"""
        self.llm_service_logger = llm_service_logger
        # Load configuration
        self.config = ConfigLoader.load_config()
        
    def _is_crypto_related(self, message_content: str, register_keywords: Optional[str] = None, 
                          register_hashtags: Optional[str] = None) -> bool:
        """
        Check if a message is crypto-related.
        
        Args:
            message_content: Content of the message to check
            register_keywords: Optional keywords from register table to filter crypto assets
            register_hashtags: Optional hashtags from register table to filter content
            
        Returns:
            bool: True if the message is crypto-related, False otherwise
        """
        try:
            # Check hashtag filtering first if specified
            if register_hashtags:
                # Split hashtags by comma and clean them
                hashtags = [tag.strip().lower().lstrip('#') for tag in register_hashtags.split(',') if tag.strip()]
                # Check if any of the specified hashtags are in the message
                content_lower = message_content.lower()
                for hashtag in hashtags:
                    # Create regex pattern to match hashtags as whole words
                    # This will match #hashtag or #hashtag followed by punctuation or space
                    pattern = r'#' + re.escape(hashtag) + r'(?=[\s\.\,\!\?\:\;\)]|$)'
                    if re.search(pattern, content_lower, re.IGNORECASE):
                        return True
            
            # If register has specific keywords, only track those assets
            # Otherwise, track all crypto assets by default
            if register_keywords:
                # Split keywords by comma and clean them
                keywords = [kw.strip().lower() for kw in register_keywords.split(',') if kw.strip()]
                # Check if any of the specified keywords are in the message
                content_lower = message_content.lower()
                for keyword in keywords:
                    if keyword in content_lower:
                        return True
                return False
            else:
                # No specific keywords specified, track all crypto assets
                crypto_keywords = [
                    r'\b(bitcoin|btc)\b',
                    r'\b(ethereum|eth)\b',
                    r'\b(solana|sol)\b',
                    r'\b(cardano|ada)\b',
                    r'\b(polkadot|dot)\b',
                    r'\b(dogecoin|doge)\b',
                    r'\b(chainlink|link)\b',
                    r'\b(uniswap|uni)\b',
                    r'\b(litecoin|ltc)\b',
                    r'\b(bitcoin cash|bch)\b',
                    r'\b(stellar|xlm)\b',
                    r'\b(tron|trx)\b',
                    r'\b(monero|xmr)\b',
                    r'\b(crypto|blockchain|defi|nft|nfts|web3|token|coin|altcoin|dex|exchange)\b',
                    r'\b(bullish|bearish|pump|dump|fomo|fud|whale|diamond hands|paper hands)\b',
                    r'\b(to the moon|hodl|dyor|fudster)\b',
                    r'\b(sats|satoshis|wei|gwei|lambo)\b',
                    r'\b(ico|ido|ieo|sto)\b',
                    r'\b(wallet|private key|public key|seed phrase)\b',
                    r'\b(market cap|marketcap|ath|atl|roi|apy|apr)\b'
                ]
                
                # Convert message to lowercase for case-insensitive matching
                content_lower = message_content.lower()
                
                # Check if any crypto keyword is present
                for pattern in crypto_keywords:
                    if re.search(pattern, content_lower, re.IGNORECASE):
                        return True
                        
            return False
        except Exception as e:
            with self.llm_service_logger.begin_section("Crypto Analysis") as section:
                section.log_step("Error checking if message is crypto-related")
                section.log_step(f"Error details - Error: {str(e)}")
            return False
    
    def _get_crypto_system_prompt(self) -> str:
        """
        Get the system prompt for crypto-related analysis.
        
        Returns:
            str: System prompt for crypto analysis
        """
        # Get crypto system prompt from config, with fallback to default
        crypto_config = self.config.get('crypto', {})
        return crypto_config.get('crypto_system_prompt', crypto_config.get('system_prompt', """You are a cryptocurrency market analyst and trading assistant. 
        You specialize in analyzing market sentiment, identifying trading opportunities, 
        and providing actionable insights based on cryptocurrency-related messages.
        Focus on price movements, market trends, sentiment analysis, and trading signals.
        Provide concise, factual analysis with clear recommendations when appropriate."""))
    
    def _get_crypto_user_prompt(self) -> str:
        """
        Get the user prompt for crypto-related analysis.
        
        Returns:
            str: User prompt for crypto analysis
        """
        # Get crypto user prompt from config, with fallback to default
        crypto_config = self.config.get('crypto', {})
        return crypto_config.get('crypto_user_prompt', crypto_config.get('user_prompt', """Analyze the following cryptocurrency-related message and provide insights:

{message_content}

Please focus on:
1. Identifying any mentioned cryptocurrencies or tokens
2. Detecting market sentiment (bullish/bearish/neutral)
3. Noting any price predictions or trading signals
4. Highlighting key market-moving information
5. Providing a concise summary of the main points

Format your response clearly and concisely."""))
    
    async def process_message_with_llm(self, register_data: Dict[str, Any], 
                                      message_content: str,
                                      context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a message using the LLM based on register configuration.
        Automatically uses crypto-specific prompts for crypto-related messages.
        
        Args:
            register_data: Register data containing LLM configuration
            message_content: Content of the message to process
            context: Additional context for processing
            
        Returns:
            Dict[str, Any]: Processing result with LLM response
        """
        try:
            # Log message content snippet for debugging in JSON format
            content_snippet = message_content[:50] + ('...' if len(message_content) > 50 else '')
            # Enhanced Implementation: Replace with structured JSON logging
            with self.llm_service_logger.begin_section("LLM Processing") as section:
                section.log_step("Processing message with LLM")
                section.log_step("Message data: ")
            
            # Get keywords and hashtags from register data
            register_keywords = register_data.get('keyword')
            register_hashtags = register_data.get('hashtag')
            
            # Check if the message is crypto-related based on register keywords and hashtags
            is_crypto = self._is_crypto_related(message_content, register_keywords, register_hashtags)
            
            with self.llm_service_logger.begin_section("LLM Processing") as section:
                section.log_step(f"Message crypto-related check: {is_crypto}")
            
            # Prepare context
            if context is None:
                context = {}
            
            context['message_content'] = message_content
            
            # Add date information to context if not already present
            if 'date_info' not in context:
                from datetime import datetime
                context['date_info'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Include date context information in context if available
            if 'date_context' in context:
                # Already provided by the caller (historical data indicator)
                with self.llm_service_logger.begin_section("LLM Processing") as section:
                    section.log_step(f"Date context provided: {context['date_context']}")
            elif 'date_range' in context:
                date_range = context['date_range']
                start_date = date_range.get('start')
                end_date = date_range.get('end')
                if start_date and end_date:
                    context['date_context'] = f"This analysis covers messages from {start_date} to {end_date}"
                elif start_date:
                    context['date_context'] = f"This analysis covers messages from {start_date} onwards"
                elif end_date:
                    context['date_context'] = f"This analysis covers messages up to {end_date}"
                else:
                    context['date_context'] = "This analysis covers historical messages"
                section.log_step(f"Date range context created: {context['date_context']}")
            elif 'processing_timestamp' in context:
                # Include processing timestamp if date_range is not specified
                timestamp = context['processing_timestamp']
                context['date_context'] = f"This analysis was performed on {timestamp}"
                section.log_step(f"Processing timestamp context: {context['date_context']}")
            
            # Enhanced reply context handling
            if 'is_reply' in context and context['is_reply']:
                if 'processing_original_message' in context and context['processing_original_message']:
                    # We're processing the original message instead of the reply
                    if 'reply_message_content' in context:
                        # Include information that this is the original message being processed
                        context['message_content'] = f"{message_content}\n\n[Note: This is the original message being processed instead of a brief reply: \"{context['reply_message_content'][:100]}{'...' if len(context['reply_message_content']) > 100 else ''}\"]"
                        section.log_step("Processing original message instead of reply")
                elif 'original_message' in context and context['original_message']:
                    # When both reply and original are combined, format them appropriately for LLM processing
                    if message_content.startswith("Reply: ") and "\n\nOriginal Message: " in message_content:
                        section.log_step("Processing combined reply and original message")
                        # The message_content already contains both, so we just need to ensure context reflects this
                        context['message_content'] = message_content
                    else:
                        # Fallback to previous behavior if not already combined
                        context['message_content'] = f"Reply: {message_content}\n\nOriginal Message: {context['original_message']}"
                elif 'reply_context' in context:
                    # Include reply context when original message is not available
                    section.log_step(f"Processing reply with context: {context['reply_context']}")
                    context['message_content'] = f"Reply: {message_content}\n\n{context['reply_context']}"
            
            # Use appropriate prompts based on whether the message is crypto-related
            if is_crypto:
                section.log_step("Using crypto-specific prompts for message processing")
                system_prompt = self._get_crypto_system_prompt()
                user_prompt = self._get_crypto_user_prompt()
            else:
                # Use prompts from register data or defaults
                system_prompt = register_data.get('llm_system_prompt', 
                                                'You are a helpful assistant.')
                user_prompt = register_data.get('llm_user_prompt', 
                                              'Please analyze the following message: {message_content}')
            
            # Log prompts for debugging
            section.log_step("System prompt")
            section.log_step("User prompt")
            
            # Create LLM helper from register data using factory pattern
            section.log_step("Creating LLM helper from register data")
            try:
                llm_helper = create_llm_helper_from_register(register_data)
                section.log_step("Successfully created LLM helper")
            except Exception as helper_error:
                section.log_step("Failed to create LLM helper")
                section.log_step(f"Helper creation error: {str(helper_error)}")
                # Fallback behavior: return original content with error indication
                return {
                    'success': False,
                    'error': f"LLM helper creation failed: {str(helper_error)}",
                    'response': f"[LLM Processing Failed] Original content: {message_content}",
                    'json_data': None,
                    'token_count': 0,
                    'is_crypto_related': is_crypto
                }
            
            # Generate response using system and user prompts
            section.log_step("Generating LLM response")
            response = await llm_helper.generate_response(system_prompt, user_prompt, context)
            
            # Validate response
            if not validate_llm_response(response):
                section.log_step("LLM response validation failed")
                fake_detection = detect_fake_content(response)
                if fake_detection["is_fake"]:
                    section.log_step("Fake content detected in LLM response")
                    section.log_step(f"Fake detection details - Confidence: {fake_detection['confidence']}, Reasons: {fake_detection['reasons']}")
                    response = "Unable to generate a valid response due to content validation issues."
                else:
                    section.log_step("Response appears to be placeholder content")
                    response = "Unable to generate a valid response."
            
            # Ensure the response is complete and coherent
            response = self._ensure_complete_response(response)
            
            result = {
                'success': True,
                'response': response,
                'json_data': None,
                'token_count': llm_helper.get_token_count(response)
            }
            
            section.log_step("Successfully generated LLM response")
            section.log_step(f"Token count: {result['token_count']}")
            
            # If this is a crypto-related message, perform additional validation
            result['is_crypto_related'] = is_crypto
            
            if is_crypto:
                section.log_step("Message identified as crypto-related")
                # Validate trading signal content for crypto-related messages
                signal_validation = validate_trading_signal_content(response)
                if not signal_validation["is_valid"]:
                    section.log_step("Invalid trading signal detected in crypto-related message")
                    section.log_step(f"Validation details - Confidence: {signal_validation['confidence']}, Reasons: {signal_validation['reasons']}")
                    # Mark as unsuccessful if trading signal validation fails for crypto messages
                    if signal_validation["confidence"] > 0.8:  # High confidence in invalid signal
                        result['success'] = False
                        result['error'] = "Invalid trading signal detected"
            
            section.log_step("Completed LLM processing")
            section.log_step(f"Success status - Success: {result['success']}")

            return result
            
        except Exception as e:
            with self.llm_service_logger.begin_section("LLM Processing") as section:
                section.log_step("Error processing message with LLM")
                section.log_step(f"Error details - Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'response': None,
                'json_data': None,
                'token_count': 0,
                'is_crypto_related': False
            }
            
    async def process_messages_batch_with_llm(self, register_data: Dict[str, Any], 
                                             messages: List[str]) -> List[Dict[str, Any]]:
        """
        Process a batch of messages using the LLM.
        
        Args:
            register_data: Register data containing LLM configuration
            messages: List of messages to process
            
        Returns:
            List[Dict[str, Any]]: List of processing results
        """
        try:
            # Create a single section for the entire method execution
            batch_section = self.llm_service_logger.begin_section("Batch Processing")
            section = batch_section.__enter__()
            section.log_step("Processing batch of messages with LLM")
            section.log_step(f"Batch data - Message count: {len(messages)}, Average message length: {sum(len(msg) for msg in messages) / len(messages) if messages else 0}")
            
            # Process messages sequentially instead of concurrently to ensure proper context handling
            processed_results = []
            for message in messages:
                try:
                    result = await self.process_message_with_llm(register_data, message)
                    processed_results.append(result)
                except Exception as e:
                    section.log_step("Error processing message")
                    section.log_step(f"Error details - Index: {len(processed_results)}, Error: {str(e)}")
                    processed_results.append({
                        'success': False,
                        'error': str(e),
                        'response': None,
                        'json_data': None,
                        'token_count': 0,
                        'is_crypto_related': False
                    })
            
            section.log_step("Completed batch processing")
            section.log_step(f"Processing summary - Successful count: {len([r for r in processed_results if r['success']])}")
            # Close the section before returning
            batch_section.__exit__(None, None, None)
            
            return processed_results
            
        except Exception as e:
            # Handle case where section might not be defined yet
            if 'section' in locals():
                section.log_step("Error processing batch with LLM")
                section.log_step(f"Error details - Error: {str(e)}")
                # Close the section before returning
                batch_section.__exit__(None, None, None)
            else:
                # Create a section just to log the error
                with self.llm_service_logger.begin_section("Batch Processing") as section:
                    section.log_step("Error processing batch with LLM")
                    section.log_step(f"Error details - Error: {str(e)}")
            return [{
                'success': False,
                'error': str(e),
                'response': None,
                'json_data': None,
                'token_count': 0,
                'is_crypto_related': False
            }] * len(messages)
            
    async def chat_with_history(self, register_data: Dict[str, Any], 
                               messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Chat with the LLM using message history.
        
        Args:
            register_data: Register data containing LLM configuration
            messages: List of messages with 'role' and 'content' keys
            
        Returns:
            Dict[str, Any]: Chat response
        """
        try:
            # Create a single section for the entire method execution
            chat_section = self.llm_service_logger.begin_section("Chat Processing")
            section = chat_section.__enter__()
            section.log_step("Chatting with LLM using history")
            section.log_step(f"Chat data - Message count: {len(messages)}, Roles: {[msg.get('role', 'unknown') for msg in messages]}")
            
            # Create LLM helper from register data
            llm_helper = create_llm_helper_from_register(register_data)
            
            # Generate response with history
            response = await llm_helper.generate_response_with_history(messages)
            
            # Validate response
            if not validate_llm_response(response):
                section.log_step("LLM chat response validation failed")
                section.log_step(f"Response snippet: {response[:100] + '...' if len(response) > 100 else response}")
                response = "Unable to generate a valid response."
            
            # Ensure the response is complete and coherent
            response = self._ensure_complete_response(response)
            
            result = {
                'success': True,
                'response': response,
                'token_count': llm_helper.get_token_count(response)
            }
            
            section.log_step("Successfully completed chat with history")
            section.log_step(f"Token count: {result['token_count']}")
            # Close the section before returning
            chat_section.__exit__(None, None, None)
            
            return result
            
        except Exception as e:
            # Handle case where section might not be defined yet
            if 'section' in locals():
                section.log_step("Error in chat with history")
                section.log_step(f"Error details - Error: {str(e)}")
                # Close the section before returning
                chat_section.__exit__(None, None, None)
            else:
                # Create a section just to log the error
                with self.llm_service_logger.begin_section("Chat Processing") as section:
                    section.log_step("Error in chat with history")
                    section.log_step(f"Error details - Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'response': None,
                'token_count': 0
            }
            
    async def process_crypto_analysis(self, register_data: Dict[str, Any], 
                                    market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process crypto market analysis using LLM.
        
        Args:
            register_data: Register data containing LLM configuration
            market_data: Dictionary containing market data to analyze
            
        Returns:
            Dict[str, Any]: Crypto analysis result
        """
        crypto_section = None  # Initialize to None to handle early exceptions
        try:
            # Create a single section for the entire method execution
            crypto_section = self.llm_service_logger.begin_section("Crypto Analysis")
            section = crypto_section.__enter__()
            section.log_step("Processing crypto analysis with LLM")
            section.log_step(f"Crypto data - Market data keys: {list(market_data.keys()) if market_data else []}")
            
            # Create LLM helper optimized for crypto analysis
            llm_helper = create_llm_helper_for_crypto_analysis(register_data)
            
            # Generate crypto analysis
            analysis_result = await llm_helper.generate_crypto_analysis(market_data)
            
            # Extract trading signals from response
            trading_signals = extract_trading_signals_from_response(analysis_result['response'])
            
            result = {
                'success': True,
                'analysis': analysis_result,
                'trading_signals': trading_signals,
                'token_count': analysis_result.get('token_count', 0)
            }
            
            section.log_step("Successfully processed crypto analysis")
            section.log_step(f"Token count: {result['token_count']}")
            # Close the section before returning
            if crypto_section is not None:
                crypto_section.__exit__(None, None, None)

            return result

        except Exception as e:
            # Handle case where section might not be defined yet
            if 'section' in locals():
                section.log_step("Error processing crypto analysis")
                section.log_step(f"Error details - Error: {str(e)}")
            # Close the section before returning (only if it was created)
            if crypto_section is not None:
                crypto_section.__exit__(None, None, None)
            else:
                # Create a section just to log the error
                with self.llm_service_logger.begin_section("Crypto Analysis") as section:
                    section.log_step("Error processing crypto analysis")
                    section.log_step(f"Error details - Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'analysis': None,
                'trading_signals': [],
                'token_count': 0
            }
            
    async def generate_unique_content(self, register_data: Dict[str, Any], 
                                    topic: str, content_type: str = "article",
                                    style_guide: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate unique content using LLM.
        
        Args:
            register_data: Register data containing LLM configuration
            topic: Topic to generate content about
            content_type: Type of content to generate
            style_guide: Optional style guide for content generation
            
        Returns:
            Dict[str, Any]: Generated content result
        """
        content_section = None  # Initialize to None to handle early exceptions
        try:
            # Create a single section for the entire method execution
            content_section = self.llm_service_logger.begin_section("Content Generation")
            section = content_section.__enter__()
            section.log_step("Generating unique content")
            section.log_step(f"Content data - Content type: {content_type}, Topic: {topic}")
            
            # Create LLM helper optimized for crypto analysis
            llm_helper = create_llm_helper_for_crypto_analysis(register_data)
            
            # Generate unique content
            content = await llm_helper.generate_unique_content(topic, content_type, style_guide)
            
            result = {
                'success': True,
                'content': content,
                'content_type': content_type,
                'topic': topic,
                'token_count': llm_helper.get_token_count(content)
            }
            
            section.log_step("Successfully generated unique content")
            section.log_step(f"Token count: {result['token_count']}")
            # Close the section before returning
            if content_section is not None:
                content_section.__exit__(None, None, None)

            return result

        except Exception as e:
            # Handle case where section might not be defined yet
            if 'section' in locals():
                section.log_step("Error generating unique content")
                section.log_step(f"Error details - Error: {str(e)}")
            # Close the section before returning (only if it was created)
            if content_section is not None:
                content_section.__exit__(None, None, None)
            else:
                # Create a section just to log the error
                with self.llm_service_logger.begin_section("Content Generation") as section:
                    section.log_step("Error generating unique content")
                    section.log_step(f"Error details - Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'content': None,
                'content_type': content_type,
                'topic': topic,
                'token_count': 0
            }
            
    def _ensure_complete_response(self, response: str) -> str:
        """
        Ensure the response is complete and coherent, not cut off mid-sentence.
        
        Args:
            response: LLM response that may be incomplete
            
        Returns:
            str: Complete and coherent response
        """
        try:
            # If response is empty, return as is
            if not response or not response.strip():
                return response
                
            # Trim whitespace
            response = response.strip()
            
            # Check for placeholder content and return a failure response instead
            placeholder_indicators = [
                'XXX',
                'placeholder',
                'Note: This is a placeholder',
                'did not provide specific messages',
                'generic summary',
                'no specific information',
                'no meaningful content'
            ]
            
            is_placeholder = any(indicator.lower() in response.lower() for indicator in placeholder_indicators)
            if is_placeholder:
                return ""  # Return empty response which will be marked as failed
            
            # Check if the response ends with common sentence-ending punctuation
            if response.endswith(('.', '!', '?', '"', "'")):
                # Response appears to be complete
                return response
                
            # If the response ends with a comma, semicolon, colon, or dash, 
            # it's likely incomplete
            if response.endswith((',', ';', ':', '-', '–')):
                # Try to find the last complete sentence
                last_sentence_end = max(
                    response.rfind('.'),
                    response.rfind('!'),
                    response.rfind('?')
                )
                
                # If we found a sentence ending, truncate to that point
                if last_sentence_end > 0:
                    return response[:last_sentence_end + 1].strip()
                    
            # If we can't find a good truncation point, it's better to return
            # the incomplete response than to truncate meaninglessly
            return response
            
        except Exception as e:
            with self.llm_service_logger.begin_section("Response Processing") as section:
                section.log_step(f"Error ensuring complete response: {str(e)}")
            # Return original response if we can't process it
            return response