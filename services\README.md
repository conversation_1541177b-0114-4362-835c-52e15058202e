# Services

This directory contains service implementations that integrate LangChain and LangGraph components with the broader application architecture.

## Overview

The services layer provides high-level functionality that combines the LangChain and LangGraph components with database operations, configuration management, and other application services.

## Services

### LangChain Processor Service (`langchain_processor.py`)

This service handles processing of scraped content using LangChain for active register records during their specified posting hours.

**Key Features:**
- Processes content for active registers within their posting hours
- Checks crawling completion status before processing
- Processes unprocessed messages with LLM analysis
- Posts processed content to Telegram with enhanced quality filtering
- Ensures message uniqueness before posting
- Implements rate limiting to prevent Telegram rate limiting
- Filters out placeholder and low-quality content
- Enhanced reply-to message handling with accurate original message retrieval

**Main Functions:**
- `_processing_loop()`: Main processing loop that runs continuously
- `_process_register_content()`: Processes content for a specific register with batching
- `_process_message_with_llm()`: Processes individual messages with LLM and enhanced context
- `_post_to_telegram()`: Posts processed content to Telegram with quality validation

### LangGraph Service (`langgraph_service.py`)

This service provides a high-level interface for LangGraph operations that integrates with the existing codebase.

**Key Features:**
- Creates and runs different types of workflows
- <PERSON>les message processing workflows with enhanced context
- Supports custom workflow creation
- Provides error handling and logging
- Enhanced reply-to message processing with accurate original message retrieval

**Main Functions:**
- `create_and_run_workflow()`: Creates and runs a LangGraph workflow
- `run_message_processing_workflow()`: Runs a message processing workflow
- `run_custom_workflow()`: Runs a custom LangGraph workflow

### LLM Service (`llm_service.py`)

This service provides a high-level interface for LLM operations that integrates with the existing codebase.

**Key Features:**
- Processes messages with LLM based on register configuration
- Automatically uses crypto-specific prompts for crypto-related messages
- Processes batches of messages
- Supports chat with history
- Performs crypto analysis
- Generates unique content
- Enhanced response validation to filter placeholder content
- Improved response completeness checking

**Main Functions:**
- `process_message_with_llm()`: Processes a message using LLM with enhanced validation
- `process_messages_batch_with_llm()`: Processes a batch of messages
- `chat_with_history()`: Chats with LLM using message history
- `process_crypto_analysis()`: Processes crypto market analysis
- `generate_unique_content()`: Generates unique content

### Background Scraper Service (`background_scraper.py`)

This service handles background scraping of Telegram channels and processing of messages.

**Key Features:**
- Continuously scrapes Telegram channels and groups for new content
- Updates tg_entities with real information from Telegram
- Manages entity relationships with registers
- Implements intelligent scraping with rate limiting
- Handles historical data scraping for new entities

**Main Functions:**
- `start()`: Starts the background scraper service
- `stop()`: Stops the background scraper service
- `_scraping_loop()`: Main scraping loop that runs continuously
- `_scrape_entity_with_random_api()`: Scrapes entities using randomly selected APIs
- `_handle_new_messages()`: Processes new messages from scraping

### Database Service (`database.py`)

This service manages database connections and operations.

**Key Features:**
- Manages SQLAlchemy database connections
- Provides session management
- Handles connection pooling
- Implements error handling and logging

**Main Functions:**
- `initialize()`: Initializes the database connection pool
- `get_session()`: Gets a database session
- `close()`: Closes the database connection pool

## Integration Patterns

### Database Integration

Services integrate with the database through:
- DatabaseService for session management
- Register and Message models for data access
- Helper functions for specific operations

### Configuration Integration

Services use configuration through:
- ConfigLoader for loading settings
- Register data for per-user configuration
- Environment variables for sensitive data

### Logging Integration

All services use the centralized logging system:
- Logger class for consistent logging
- Context-specific log messages
- Error tracking and monitoring

## Usage Examples

### Using LangGraph Service

```python
from services.langgraph_service import LanggraphService

# Create service instance
langgraph_service = LanggraphService()

# Run a message processing workflow
messages = [{"role": "user", "content": "Hello, world!"}]
context = {"project_id": 123}
result = await langgraph_service.run_message_processing_workflow(messages, context)
```

### Using LLM Service

```python
from services.llm_service import LLMService

# Create service instance
llm_service = LLMService()

# Process a message
register_data = {
    "model_selection": 1,
    "llm_api_key": "api-key",
    "llm_temperature": 0.7
}
message_content = "What is the price of Bitcoin?"
result = await llm_service.process_message_with_llm(register_data, message_content)
```

## Error Handling

All services implement comprehensive error handling:
- Try/catch blocks for exception handling
- Detailed error logging
- Graceful degradation
- Return consistent error formats

## Performance Considerations

Services are designed with performance in mind:
- Asynchronous operations where appropriate
- Concurrent processing for batch operations
- Efficient database queries
- Resource cleanup and management
- Rate limiting to prevent API throttling
- Content quality filtering to reduce unnecessary processing

## Recent Enhancements

### Content Quality Filtering
- Enhanced validation to detect and filter placeholder content (e.g., "XXX" values)
- Improved response validation to prevent low-quality content from being posted
- Added checks for meaningful content length and structure
- Multi-level validation in LLM service, helpers, and core modules

### Reply Handling Improvements
- Enhanced reply-to message processing with accurate original message retrieval
- Improved context handling for reply messages using both message_id and entities_id matching
- Better handling of message thread IDs for forum topics
- Enhanced conversation context preservation

### Rate Limiting and Batching
- Implemented batching to limit the number of messages processed at once (reduced from 10 to 3 messages)
- Added delays between message posts to avoid Telegram rate limiting (2 seconds)
- Dynamic wait time calculation based on new messages
- Configurable processing intervals

### Historical Data Management
- Smart historical scraping that only occurs when no existing content is present
- Improved content deduplication to prevent repeated posts
- Date context information for both historical and recent data
- Configurable historical scraping window

### Performance Optimizations
- Reduced database load with optimized querying
- Improved error handling and logging for better debugging
- Enhanced state management for tracking last processed messages
- Better resource cleanup and management

## Project Integration

This component is part of the larger Telegram Scraper project. For more information about the overall architecture and how this component fits in, see the [main project README](../README.md).