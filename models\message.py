from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Integer, String, Text, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from models.base import Base

class Message(Base):
    __tablename__ = 'messages'

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    project_id: Mapped[int] = mapped_column(Integer, nullable=False)
    message_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    entities_id: Mapped[int] = mapped_column(Integer, ForeignKey('tg_entities.id'), nullable=True)
    type: Mapped[str] = mapped_column(String(50), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    message: Mapped[str] = mapped_column(Text, nullable=False)
    message_used: Mapped[str] = mapped_column(Text, nullable=True)
    reply_to: Mapped[int] = mapped_column(BigInteger, nullable=True)
    reply_time: Mapped[int] = mapped_column(BigInteger, nullable=True)
    created_at: Mapped[int] = mapped_column(BigInteger, nullable=False)

    # Relationships
    entity: Mapped["TgEntity"] = relationship("TgEntity", back_populates="messages")