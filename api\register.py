from fastapi import APIRouter, Depends, HTTPException, status, Header, Request
from pydantic import BaseModel
from typing import Optional, List
from utils.logger import Logger
import time
from sqlalchemy.ext.asyncio import AsyncSession
from api.auth import verify_bearer_token
from helpers.api_helpers import create_success_response, raise_http_exception
from helpers.validation import (
    body, 
    validate_data, 
    raise_validation_error
)
from services.database import DatabaseService
from models.register import Register
from models.tg_entity import TgEntity
from models.register_tg_entity import RegisterTgEntity
from helpers.tg_entity_helpers import get_or_create_tg_entity, update_tg_entity_details
import json
import time

# Add the import for the new message helper

from helpers.telegram_helpers import check_session_file_exists
from helpers.message_helpers import check_if_entities_are_new, message_helper_logger
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat
from helpers.telegram_helpers import create_telegram_client

router = APIRouter()
register_logger = Logger("api.register")

# Pydantic models for response only
class RegisterResponse(BaseModel):
    id: int
    project_id: Optional[int] = None
    phone_number: str
    api_id: int
    tg_bot_token: str
    tg_chat_id: Optional[str] = None
    message_thread_id: Optional[int] = None
    scheduler_to_post: int
    active_post_hour_start: int
    active_post_hour_end: int
    active_reply_hour_start: int
    active_reply_hour_end: int
    keyword: Optional[str] = None
    hashtag: Optional[str] = None
    model_selection: int
    llm_api_key: str
    llm_max_token: int
    llm_temperature: float
    llm_top_p: float
    llm_system_prompt: str
    llm_user_prompt: str
    reply_mentions: int
    expiry_date: int
    active: int
    created_at: int
    tg_entities: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": 1,
                "project_id": 1001,
                "phone_number": "+1234567890",
                "api_id": 1234567,
                "tg_bot_token": "123456789:ABCdefGHIjklMNOpqrSTUvwxYZ1234567890",
                "tg_chat_id": "-1001234567890",
                "message_thread_id": 12345,
                "scheduler_to_post": 1,
                "active_post_hour_start": 9,
                "active_post_hour_end": 17,
                "active_reply_hour_start": 9,
                "active_reply_hour_end": 17,
                "keyword": "crypto,bitcoin",
                "hashtag": "#crypto,#bitcoin",
                "model_selection": 1,
                "llm_api_key": "sk-...",
                "llm_max_token": 1000,
                "llm_temperature": 0.7,
                "llm_top_p": 0.9,
                "llm_system_prompt": "You are a helpful assistant.",
                "llm_user_prompt": "Analyze this content.",
                "reply_mentions": 1,
                "expiry_date": 1731081600,
                "active": 1,
                "created_at": 1700000000,
                "tg_entities": "channel1,channel2/@12345,@group1,@group2/@67890"
            }
        }

def safe_int_conversion(value, default=0):
    """Safely convert a value to int, returning default if conversion fails."""
    try:
        return int(value) if value is not None and str(value).strip() else default
    except (ValueError, TypeError):
        return default

def safe_float_conversion(value, default=0.0):
    """Safely convert a value to float, returning default if conversion fails."""
    try:
        return float(value) if value is not None and str(value).strip() else default
    except (ValueError, TypeError):
        return default

@router.post("", response_model=dict, status_code=status.HTTP_201_CREATED,
             summary="Create a new register entry",
             description="Create a new register entry with the provided information. This endpoint requires authentication with a bearer token. The tg_entities field supports multiple formats: JSON array with internal_id and message_thread_id, or comma-separated strings in formats like internal_channel_id, internal_group_id/message_thread_id, @username, @username/message_thread_id.",
             response_description="The created register entry")
async def create_register(request: Request, token: str = Depends(verify_bearer_token)):
    """Create a new register entry."""
    # Parse JSON body
    try:
        body_data = await request.json()
    except:
        raise_http_exception("Invalid JSON body", 400)
    
    # Set up validation chains for all fields
    chains = body([
        "phone_number", "api_id", "api_hash", "tg_bot_token", "tg_chat_id",
        "scheduler_to_post", "active_post_hour_start", "active_post_hour_end",
        "active_reply_hour_start", "active_reply_hour_end", "keyword", "hashtag",
        "model_selection", "llm_api_key", "llm_max_token",
        "llm_temperature", "llm_top_p", "llm_system_prompt", "llm_user_prompt",
        "reply_mentions", "expiry_date", "active", "project_id",
        "message_thread_id", "tg_entities"
    ])
    
    # Apply validation rules
    chains["phone_number"].is_required().is_phone_number()
    chains["api_id"].is_required().is_int().custom(lambda x: safe_int_conversion(x) > 0, "API ID must be a positive integer")
    chains["api_hash"].is_required().is_string().custom(lambda x: len(x) > 0, "API hash cannot be empty")
    chains["tg_bot_token"].is_required().is_string().custom(lambda x: len(x) > 0, "Telegram bot token cannot be empty")
    chains["tg_chat_id"].is_string()
    chains["scheduler_to_post"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 1, "Scheduler to post must be 0 or 1")
    chains["active_post_hour_start"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 23, "Active post hour start must be between 0 and 23")
    chains["active_post_hour_end"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 23, "Active post hour end must be between 0 and 23")
    chains["active_reply_hour_start"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 23, "Active reply hour start must be between 0 and 23")
    chains["active_reply_hour_end"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 23, "Active reply hour end must be between 0 and 23")
    chains["keyword"].is_string()
    chains["hashtag"].is_string()
    chains["model_selection"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 10, "Model selection must be between 0 and 10")
    chains["llm_api_key"].is_required().is_string().custom(lambda x: len(x) > 0, "LLM API key cannot be empty")
    chains["llm_max_token"].is_int().custom(lambda x: safe_int_conversion(x) > 0, "Max token must be a positive integer")
    chains["llm_temperature"].is_float().custom(lambda x: 0.0 <= safe_float_conversion(x) <= 1.0, "Temperature must be between 0.0 and 1.0")
    chains["llm_top_p"].is_float().custom(lambda x: 0.0 <= safe_float_conversion(x) <= 1.0, "Top P must be between 0.0 and 1.0")
    chains["llm_system_prompt"].is_string()
    chains["llm_user_prompt"].is_string()
    chains["reply_mentions"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 1, "Reply mentions must be 0 or 1")
    chains["expiry_date"].is_int().custom(lambda x: safe_int_conversion(x) >= 0, "Expiry date must be a non-negative integer")
    chains["active"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 1, "Active must be 0 or 1")
    chains["message_thread_id"].is_int().custom(lambda x: safe_int_conversion(x) >= 0 if x is not None else True, "Message thread ID must be a non-negative integer")
    chains["project_id"].is_int().custom(lambda x: safe_int_conversion(x) > 0, "Project ID must be a positive integer")
    chains["tg_entities"].is_string()
    chains["tg_entities"].is_string()
    
    # Validate data
    result = validate_data(body_data, chains)
    if not result.is_valid():
        raise_validation_error(result)
    
    phone_number = body_data["phone_number"]
    register_logger.info("Creating new register entry for phone_number", phone_number=phone_number)
    
    # Check if session file exists before creating register entry
    if not check_session_file_exists(phone_number):
        raise_http_exception("Session file does not exist. Please verify OTP first.", 400)
    
    # Create register entry in database
    db_service = request.app.state.db_service
    session = await db_service.get_session()
    try:
        # Use select statement with execute instead of query
        from sqlalchemy import select
        stmt = select(Register).where(Register.phone_number == phone_number)
        result = await session.execute(stmt)
        existing_user = result.scalars().first()
        
        if existing_user:
            raise_http_exception("User already exists. Use update endpoint instead.", 409)
        
        # Get current Unix epoch time
        current_time = int(time.time())
        
        # Create new register entry
        register_entry = Register(
            phone_number=phone_number,
            api_id=safe_int_conversion(body_data["api_id"]),
            api_hash=body_data["api_hash"],
            tg_bot_token=body_data["tg_bot_token"],
            tg_chat_id=body_data.get("tg_chat_id"),
            message_thread_id=safe_int_conversion(body_data.get("message_thread_id")) if body_data.get("message_thread_id") is not None else None,
            scheduler_to_post=safe_int_conversion(body_data.get("scheduler_to_post", 0)),
            active_post_hour_start=safe_int_conversion(body_data.get("active_post_hour_start", 0)),
            active_post_hour_end=safe_int_conversion(body_data.get("active_post_hour_end", 23)),
            active_reply_hour_start=safe_int_conversion(body_data.get("active_reply_hour_start", 0)),
            active_reply_hour_end=safe_int_conversion(body_data.get("active_reply_hour_end", 23)),
            keyword=body_data.get("keyword"),
            hashtag=body_data.get("hashtag"),
            model_selection=safe_int_conversion(body_data.get("model_selection", 0)),
            llm_api_key=body_data.get("llm_api_key", ""),
            llm_max_token=safe_int_conversion(body_data.get("llm_max_token", 1000)),
            llm_temperature=safe_float_conversion(body_data.get("llm_temperature", 0.7)),
            llm_top_p=safe_float_conversion(body_data.get("llm_top_p", 0.9)),
            llm_system_prompt=body_data.get("llm_system_prompt", ""),
            llm_user_prompt=body_data.get("llm_user_prompt", ""),
            reply_mentions=safe_int_conversion(body_data.get("reply_mentions", 0)),
            expiry_date=safe_int_conversion(body_data.get("expiry_date", current_time + 31536000)),  # 1 year from now
            active=safe_int_conversion(body_data.get("active", 1)),
            is_verify=1 if check_session_file_exists(phone_number) else 0,  # Automatically set based on session file existence
            project_id=safe_int_conversion(body_data.get("project_id", 0)),  # Add project_id
            created_at=current_time  # Unix epoch time
        )
        
        session.add(register_entry)
        await session.commit()
        await session.refresh(register_entry)
        
        # Parse entity names from tg_entities for initial message creation
        entity_names = []
        if body_data.get("tg_entities"):
            # Parse the entities - could be a simple comma-separated string or JSON array
            entities_data = []
            try:
                # Try to parse as JSON first (for new format with internal_id and message_thread_id)
                entities_json = json.loads(body_data.get("tg_entities", ""))
                if isinstance(entities_json, list):
                    entities_data = entities_json
                else:
                    # If it's not a list, treat as comma-separated string
                    entities_data = [e.strip() for e in body_data.get("tg_entities", "").split(",") if e.strip()]
            except json.JSONDecodeError:
                # If JSON parsing fails, treat as comma-separated string
                entities_data = [e.strip() for e in body_data.get("tg_entities", "").split(",") if e.strip()]
            
            # Extract entity names from entities_data
            for entity_data in entities_data:
                if isinstance(entity_data, dict):
                    # New format with internal_id and message_thread_id
                    entity_name = entity_data.get("name") or entity_data.get("internal_id")
                else:
                    entity_name = str(entity_data)
                if entity_name:
                    entity_names.append(entity_name)
            
            # Check if entities are new
            are_new_entities = await check_if_entities_are_new(session, register_entry.project_id, entity_names)
        
        # Per requirements, do not create messages during registration
        # Only crawling process should create message records
        message_helper_logger.info(
            "Skipping initial posting message creation for project %s during registration", register_entry.project_id
        )
        
        # Handle tg_entities relationship
        if body_data.get("tg_entities"):
            await _handle_register_tg_entities(session, register_entry, body_data.get("tg_entities", ""))
        
        register_data = RegisterResponse(
            id=register_entry.id,
            project_id=register_entry.project_id,
            phone_number=register_entry.phone_number,
            api_id=register_entry.api_id,
            tg_bot_token=register_entry.tg_bot_token,
            tg_chat_id=register_entry.tg_chat_id,
            message_thread_id=register_entry.message_thread_id,
            scheduler_to_post=register_entry.scheduler_to_post,
            active_post_hour_start=register_entry.active_post_hour_start,
            active_post_hour_end=register_entry.active_post_hour_end,
            active_reply_hour_start=register_entry.active_reply_hour_start,
            active_reply_hour_end=register_entry.active_reply_hour_end,
            keyword=register_entry.keyword,
            hashtag=register_entry.hashtag,
            model_selection=register_entry.model_selection,
            llm_api_key=register_entry.llm_api_key,
            llm_max_token=register_entry.llm_max_token,
            llm_temperature=register_entry.llm_temperature,
            llm_top_p=register_entry.llm_top_p,
            llm_system_prompt=register_entry.llm_system_prompt,
            llm_user_prompt=register_entry.llm_user_prompt,
            reply_mentions=register_entry.reply_mentions,
            expiry_date=register_entry.expiry_date,
            active=register_entry.active,
            created_at=register_entry.created_at,  # Already Unix epoch time
            tg_entities=body_data.get("tg_entities")
        )
    except Exception as e:
        await session.rollback()
        register_logger.error("Error creating register entry", error=str(e))
        raise
    finally:
        await session.close()
    
    # Trigger background scraping for this user
    if hasattr(request.app.state, 'bg_scraper_service'):
        register_logger.info("Triggering background scraping for new register entry with phone_number", phone_number=phone_number)
        # Notify the background scraper service about the new registration
        await request.app.state.bg_scraper_service.add_user(phone_number)
    
    # Return the response as a dictionary
    return create_success_response(
        "Register entry created successfully",
        register_data.dict(),
        201
    ).dict()

@router.post("/update", response_model=dict, status_code=status.HTTP_200_OK,
             summary="Update an existing register entry",
             description="Update an existing register entry with the provided information. This endpoint requires authentication with a bearer token. The tg_entities field supports multiple formats: JSON array with internal_id and message_thread_id, or comma-separated strings in formats like internal_channel_id, internal_group_id/message_thread_id, @username, @username/message_thread_id.",
             response_description="The updated register entry")
async def update_register(request: Request, token: str = Depends(verify_bearer_token)):
    """Update an existing register entry."""
    # Parse JSON body
    try:
        body_data = await request.json()
    except:
        raise_http_exception("Invalid JSON body", 400)
    
    # Set up validation chains for all fields
    chains = body([
        "phone_number", "api_id", "api_hash", "tg_bot_token", "tg_chat_id",
        "scheduler_to_post", "active_post_hour_start", "active_post_hour_end",
        "active_reply_hour_start", "active_reply_hour_end", "keyword", "hashtag",
        "model_selection", "llm_api_key", "llm_max_token",
        "llm_temperature", "llm_top_p", "llm_system_prompt", "llm_user_prompt",
        "reply_mentions", "expiry_date", "active", "project_id",
        "message_thread_id", "tg_entities"
    ])
    
    # Apply validation rules
    chains["phone_number"].is_required().is_phone_number()
    chains["api_id"].is_int().custom(lambda x: safe_int_conversion(x) > 0 if x is not None else True, "API ID must be a positive integer")
    chains["api_hash"].is_string().custom(lambda x: len(x) > 0 if x is not None else True, "API hash cannot be empty")
    chains["tg_bot_token"].is_string().custom(lambda x: len(x) > 0 if x is not None else True, "Telegram bot token cannot be empty")
    chains["tg_chat_id"].is_string()
    chains["scheduler_to_post"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 1 if x is not None else True, "Scheduler to post must be 0 or 1")
    chains["active_post_hour_start"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 23 if x is not None else True, "Active post hour start must be between 0 and 23")
    chains["active_post_hour_end"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 23 if x is not None else True, "Active post hour end must be between 0 and 23")
    chains["active_reply_hour_start"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 23 if x is not None else True, "Active reply hour start must be between 0 and 23")
    chains["active_reply_hour_end"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 23 if x is not None else True, "Active reply hour end must be between 0 and 23")
    chains["keyword"].is_string()
    chains["hashtag"].is_string()
    chains["model_selection"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 10 if x is not None else True, "Model selection must be between 0 and 10")
    chains["llm_api_key"].is_string().custom(lambda x: len(x) > 0 if x is not None else True, "LLM API key cannot be empty")
    chains["llm_max_token"].is_int().custom(lambda x: safe_int_conversion(x) > 0 if x is not None else True, "Max token must be a positive integer")
    chains["llm_temperature"].is_float().custom(lambda x: 0.0 <= safe_float_conversion(x) <= 1.0 if x is not None else True, "Temperature must be between 0.0 and 1.0")
    chains["llm_top_p"].is_float().custom(lambda x: 0.0 <= safe_float_conversion(x) <= 1.0 if x is not None else True, "Top P must be between 0.0 and 1.0")
    chains["llm_system_prompt"].is_string()
    chains["llm_user_prompt"].is_string()
    chains["reply_mentions"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 1 if x is not None else True, "Reply mentions must be 0 or 1")
    chains["expiry_date"].is_int().custom(lambda x: safe_int_conversion(x) >= 0 if x is not None else True, "Expiry date must be a non-negative integer")
    chains["active"].is_int().custom(lambda x: 0 <= safe_int_conversion(x) <= 1 if x is not None else True, "Active must be 0 or 1")
    chains["message_thread_id"].is_int().custom(lambda x: safe_int_conversion(x) >= 0 if x is not None else True, "Message thread ID must be a non-negative integer")
    chains["project_id"].is_int().custom(lambda x: safe_int_conversion(x) > 0 if x is not None else True, "Project ID must be a positive integer")
    chains["tg_entities"].is_string()
    
    # Validate data
    result = validate_data(body_data, chains)
    if not result.is_valid():
        raise_validation_error(result)
    
    phone_number = body_data["phone_number"]
    register_logger.info("Updating register entry for phone_number", phone_number=phone_number)
    
    # Check if session file exists before updating register entry
    if not check_session_file_exists(phone_number):
        raise_http_exception("Session file does not exist. Please verify OTP first.", 400)
    
    # Update register entry in database
    db_service = request.app.state.db_service
    session = await db_service.get_session()
    try:
        # Use select statement with execute instead of query
        from sqlalchemy import select
        stmt = select(Register).where(Register.phone_number == phone_number)
        result = await session.execute(stmt)
        existing_user = result.scalars().first()
        
        if not existing_user:
            raise_http_exception("User not found. Please create user first.", 404)
        
        # Update fields if provided
        if "api_id" in body_data:
            existing_user.api_id = safe_int_conversion(body_data["api_id"])
        if "api_hash" in body_data:
            existing_user.api_hash = body_data["api_hash"]
        if "tg_bot_token" in body_data:
            existing_user.tg_bot_token = body_data["tg_bot_token"]
        if "tg_chat_id" in body_data:
            existing_user.tg_chat_id = body_data["tg_chat_id"]
        if "scheduler_to_post" in body_data:
            existing_user.scheduler_to_post = safe_int_conversion(body_data["scheduler_to_post"])
        if "active_post_hour_start" in body_data:
            existing_user.active_post_hour_start = safe_int_conversion(body_data["active_post_hour_start"])
        if "active_post_hour_end" in body_data:
            existing_user.active_post_hour_end = safe_int_conversion(body_data["active_post_hour_end"])
        if "active_reply_hour_start" in body_data:
            existing_user.active_reply_hour_start = safe_int_conversion(body_data["active_reply_hour_start"])
        if "active_reply_hour_end" in body_data:
            existing_user.active_reply_hour_end = safe_int_conversion(body_data["active_reply_hour_end"])
        if "keyword" in body_data:
            existing_user.keyword = body_data["keyword"]
        if "hashtag" in body_data:
            existing_user.hashtag = body_data["hashtag"]
        if "model_selection" in body_data:
            existing_user.model_selection = safe_int_conversion(body_data["model_selection"])
        if "message_thread_id" in body_data:
            existing_user.message_thread_id = safe_int_conversion(body_data["message_thread_id"]) if body_data["message_thread_id"] is not None else None
        if "llm_api_key" in body_data:
            existing_user.llm_api_key = body_data["llm_api_key"]
        if "llm_max_token" in body_data:
            existing_user.llm_max_token = safe_int_conversion(body_data["llm_max_token"])
        if "llm_temperature" in body_data:
            existing_user.llm_temperature = safe_float_conversion(body_data["llm_temperature"])
        if "llm_top_p" in body_data:
            existing_user.llm_top_p = safe_float_conversion(body_data["llm_top_p"])
        if "llm_system_prompt" in body_data:
            existing_user.llm_system_prompt = body_data["llm_system_prompt"]
        if "llm_user_prompt" in body_data:
            existing_user.llm_user_prompt = body_data["llm_user_prompt"]
        if "reply_mentions" in body_data:
            existing_user.reply_mentions = safe_int_conversion(body_data["reply_mentions"])
        if "expiry_date" in body_data:
            existing_user.expiry_date = safe_int_conversion(body_data["expiry_date"])
        if "active" in body_data:
            existing_user.active = safe_int_conversion(body_data["active"])
        # Automatically update is_verify based on session file existence
        existing_user.is_verify = 1 if check_session_file_exists(existing_user.phone_number) else 0
        if "project_id" in body_data:
            existing_user.project_id = safe_int_conversion(body_data["project_id"])  # Add project_id
        
        session.add(existing_user)
        await session.commit()
        await session.refresh(existing_user)
        
        # Per requirements, do not create messages during registration
        # Only crawling process should create message records
        if "tg_entities" in body_data:
            message_helper_logger.info(
                "Skipping initial posting message creation for project %s during registration", existing_user.project_id
            )
        
        # Per requirements, do not create messages during registration
        # Only crawling process should create message records
        if "tg_entities" in body_data:
            message_helper_logger.info(
                "Skipping initial posting message creation for project %s during registration", existing_user.project_id
            )
        
        # Handle tg_entities relationship
        if "tg_entities" in body_data:
            await _handle_register_tg_entities(session, existing_user, body_data.get("tg_entities", ""))
        
        register_data = RegisterResponse(
            id=existing_user.id,
            project_id=existing_user.project_id,
            phone_number=existing_user.phone_number,
            api_id=existing_user.api_id,
            tg_bot_token=existing_user.tg_bot_token,
            tg_chat_id=existing_user.tg_chat_id,
            message_thread_id=existing_user.message_thread_id,
            scheduler_to_post=existing_user.scheduler_to_post,
            active_post_hour_start=existing_user.active_post_hour_start,
            active_post_hour_end=existing_user.active_post_hour_end,
            active_reply_hour_start=existing_user.active_reply_hour_start,
            active_reply_hour_end=existing_user.active_reply_hour_end,
            keyword=existing_user.keyword,
            hashtag=existing_user.hashtag,
            model_selection=existing_user.model_selection,
            llm_api_key=existing_user.llm_api_key,
            llm_max_token=existing_user.llm_max_token,
            llm_temperature=existing_user.llm_temperature,
            llm_top_p=existing_user.llm_top_p,
            llm_system_prompt=existing_user.llm_system_prompt,
            llm_user_prompt=existing_user.llm_user_prompt,
            reply_mentions=existing_user.reply_mentions,
            expiry_date=existing_user.expiry_date,
            active=existing_user.active,
            created_at=existing_user.created_at,  # Already Unix epoch time
            tg_entities=body_data.get("tg_entities")
        )
    except Exception as e:
        await session.rollback()
        register_logger.error("Error updating register entry", error=str(e))
        raise
    finally:
        await session.close()
    
    # Trigger background scraping for this user if they've been activated
    if hasattr(request.app.state, 'bg_scraper_service') and safe_int_conversion(body_data.get("active", 0)) == 1:
        register_logger.info("Triggering background scraping for updated register entry with phone_number", phone_number=phone_number)
        # Notify the background scraper service about the updated registration
        await request.app.state.bg_scraper_service.add_user(phone_number)
    
    # Return the response as a dictionary
    return create_success_response(
        "Register entry updated successfully",
        register_data.dict()
    ).dict()

def _parse_entity_string(entity_str: str) -> dict:
    """
    Parse entity string to extract entity name and message_thread_id.
    Supports formats like:
    - internal_channel_id
    - internal_group_id/message_thread_id
    - @username
    - @username/message_thread_id
    
    Args:
        entity_str (str): Entity string to parse
        
    Returns:
        dict: Dictionary with 'name' and optional 'message_thread_id' keys
    """
    if '/' in entity_str:
        parts = entity_str.split('/', 1)
        entity_name = parts[0]
        try:
            message_thread_id = int(parts[1])
        except (ValueError, IndexError):
            message_thread_id = None
    else:
        entity_name = entity_str
        message_thread_id = None
        
    return {
        "name": entity_name,
        "message_thread_id": message_thread_id
    }

async def _update_tg_entities_with_real_info(register_entry: Register, tg_entities: List[TgEntity]):
    """
    Update tg_entities with real information from Telegram.
    
    Args:
        register_entry (Register): Register entry with Telegram credentials
        tg_entities (List[TgEntity]): List of tg_entities to update
    """
    client = None
    try:
        # Create Telegram client
        client = await create_telegram_client(
            register_entry.phone_number,
            register_entry.api_id,
            register_entry.api_hash,
            register_entry.tg_bot_token
        )
        
        # Connect to Telegram
        await client.start()
        register_logger.info("Connected to Telegram for user", phone_number=register_entry.phone_number)
        
        # Update each tg_entity with real information from Telegram
        for entity in tg_entities:
            try:
                # Get entity information from Telegram
                if entity.tg_id.isdigit():
                    tg_entity = await client.get_entity(int(entity.tg_id))
                    # Determine entity type
                    if isinstance(tg_entity, Channel):
                        entity_type = "group" if getattr(tg_entity, 'megagroup', False) else "channel"
                    elif isinstance(tg_entity, Chat):
                        entity_type = "group"
                    else:
                        entity_type = "channel"
                    
                    # Get access_hash if available
                    access_hash = getattr(tg_entity, 'access_hash', None)
                    
                    # Update entity in database
                    db_service = DatabaseService({})  # We'll get session directly
                    db_session = await db_service.get_session()
                    try:
                        await update_tg_entity_details(
                            db_session,
                            int(entity.tg_id),
                            tg_entity.title if hasattr(tg_entity, 'title') else tg_entity.name,
                            str(access_hash) if access_hash else None,
                            entity_type,
                            getattr(tg_entity, 'username', None) is None
                        )
                        await db_session.commit()
                        register_logger.info("Updated tg_entity with real details", tg_id=entity.tg_id, name=tg_entity.title if hasattr(tg_entity, 'title') else tg_entity.name, type=entity_type)
                    except Exception as e:
                        register_logger.error("Failed to update tg_entity", tg_id=entity.tg_id, error=str(e))
                        await db_session.rollback()
                    finally:
                        await db_session.close()
            except Exception as e:
                register_logger.error("Error getting entity info", tg_id=entity.tg_id, error=str(e))
                
    except Exception as e:
        register_logger.error("Error connecting to Telegram", phone_number=register_entry.phone_number, error=str(e))
    finally:
        # Clean up client if it was created
        if client:
            try:
                await client.disconnect()
            except:
                pass

async def _handle_register_tg_entities(session: AsyncSession, register_entry: Register, tg_entities_str: str):
    """
    Handle the relationship between register and tg_entities.
    This function will:
    1. Remove all existing relationships for this project_id
    2. Create new relationships based on the provided tg_entities string
    3. Create new tg_entity records if they don't exist
    
    Supports multiple formats for tg_entities:
    - JSON array with internal_id and message_thread_id
    - Comma-separated strings in formats like:
      * internal_channel_id
      * internal_group_id/message_thread_id
      * @username
      * @username/message_thread_id
    
    Args:
        session (AsyncSession): Database session
        register_entry (Register): Register entry object
        tg_entities_str (str): tg_entities in supported formats
    """
    project_id = register_entry.project_id
    
    if not tg_entities_str:
        # If no entities provided, just delete existing relationships
        from sqlalchemy import delete
        stmt = delete(RegisterTgEntity).where(RegisterTgEntity.project_id == project_id)
        await session.execute(stmt)
        await session.commit()
        return
        
    # Parse the entities - could be a simple comma-separated string, JSON array, or special format
    entities_data = []
    try:
        # Try to parse as JSON first (for new format with internal_id and message_thread_id)
        entities_json = json.loads(tg_entities_str)
        if isinstance(entities_json, list):
            entities_data = entities_json
        else:
            # If it's not a list, treat as comma-separated string
            entities_data = [_parse_entity_string(entity.strip()) for entity in tg_entities_str.split(',') if entity.strip()]
    except json.JSONDecodeError:
        # If JSON parsing fails, treat as comma-separated string
        entities_data = [_parse_entity_string(entity.strip()) for entity in tg_entities_str.split(',') if entity.strip()]
    
    # First, remove all existing relationships for this project_id
    from sqlalchemy import delete
    stmt = delete(RegisterTgEntity).where(RegisterTgEntity.project_id == project_id)
    await session.execute(stmt)
    
    # Keep track of entities to update with real info
    entities_to_update = []
    
    # Process each entity
    for entity_data in entities_data:
        # Extract entity information
        if isinstance(entity_data, dict):
            entity_name = entity_data.get("name") or entity_data.get("internal_id")
            message_thread_id = entity_data.get("message_thread_id")
        else:
            # For non-dict entities, we already parsed them with _parse_entity_string
            entity_name = entity_data.get("name")
            message_thread_id = entity_data.get("message_thread_id")
            
        if not entity_name:
            continue
            
        # Get or create entity with helper function
        # For now, we'll create a placeholder since we don't have the real Telegram details yet
        tg_entity = await get_or_create_tg_entity(
            session,
            int(entity_name) if entity_name.isdigit() else 0,  # Use name as internal ID if it's numeric
            entity_name,
            None,  # access_hash will be updated when scraped
            "unknown",  # Will be updated when scraped
            None  # is_private will be updated when scraped
        )
        entity_id = tg_entity.id
        entities_to_update.append(tg_entity)
            
        # Create the relationship
        register_tg_entity = RegisterTgEntity(
            project_id=project_id,
            tg_entity_id=entity_id,
            message_thread_id=message_thread_id
        )
        session.add(register_tg_entity)
    
    # Commit all changes
    await session.commit()
    
    # Update tg_entities with real information from Telegram
    if entities_to_update:
        await _update_tg_entities_with_real_info(register_entry, entities_to_update)
