import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select
from utils.config_loader import ConfigLoader
from utils.logger import Logger
from helpers.database_helpers import format_database_url
from models.base import Base
from models.register import Register
from models.tg_entity import TgEntity
from models.tg_content import TgContent
from models.message import Message
from models.bot_state import BotState
from models.otp_request import OtpRequest

db_logger = Logger("database")

class DatabaseService:
    def __init__(self, config):
        self.config = config
        self.engine = None
        self.async_session = None
        
    async def initialize(self):
        """Initialize the database connection pool."""
        try:
            # Use the new config structure
            db_url = format_database_url(
                host=self.config['host'],
                port=3306,  # Default MySQL port
                username=self.config['username'],
                password=self.config['password'],
                database=self.config['database']
            )
            
            self.engine = create_async_engine(
                db_url,
                pool_size=10,
                max_overflow=20,
                echo=False
            )
            
            self.async_session = sessionmaker(
                bind=self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            db_logger.info("Database connection pool initialized successfully")
        except Exception as e:
            db_logger.error("Failed to initialize database connection pool", error=str(e))
            raise

    async def get_session(self) -> AsyncSession:
        """Get a database session from the pool."""
        if not self.async_session:
            raise RuntimeError("Database service not initialized")
        return self.async_session()

    async def close(self):
        """Close the database connection pool."""
        if self.engine:
            await self.engine.dispose()
            db_logger.info("Database connection pool closed")

    async def test_connection(self) -> bool:
        """Test database connection."""
        try:
            session = await self.get_session()
            try:
                # Using SQLAlchemy core instead of raw SQL
                result = await session.execute(select(1))
                return result.scalar() == 1
            finally:
                await session.close()
        except Exception as e:
            db_logger.error("Database connection test failed", error=str(e))
            return False