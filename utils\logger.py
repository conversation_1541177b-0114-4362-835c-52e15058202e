import logging
import sys
import os
import json
from datetime import datetime, timezone
from typing import Any, Dict
import threading

# Get log level from environment or config, default to INFO
log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
numeric_level = getattr(logging, log_level, logging.INFO)

# Thread-local storage for section tracking
_thread_local = threading.local()

# --- 1. Ultra-Polished Text Formatter (Short Path Only) ---
class UnifiedFormatter(logging.Formatter):
    ALLOWED_EXTRA_KEYS = {
        'request_id', 'trace_id', 'span_id',
        'user_id', 'tenant_id',
        'endpoint', 'method', 'status_code',
        'duration_ms', 'service', 'env',
        'queue_name', 'task_id', 'retry_count'
    }

    def format(self, record: logging.LogRecord) -> str:
        # Check if this message should bypass formatting
        if getattr(record, 'bypass_formatting', False):
            return record.getMessage()

        # ALWAYS extract the core message
        message = record.getMessage()
        if not isinstance(message, str):
            message = str(message)

        # Extract UTC timestamp
        utc_timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
        levelname = record.levelname

        # Build extra key=value pairs (filtered)
        extra_parts = []
        for key, value in record.__dict__.items():
            if key in self.ALLOWED_EXTRA_KEYS and value is not None:
                if isinstance(value, str) and not value.strip():
                    continue
                extra_parts.append(f"{key}={value}")

        extra_str = " ".join(extra_parts)

        # === SHORTEN PATH TO LAST 2 COMPONENTS (e.g., utils/logger.py) ===
        pathname = getattr(record, 'pathname', '')
        if not pathname:
            short_path = "<unknown>"
        else:
            # Split by both / and \ to handle cross-platform paths
            parts = pathname.replace('\\', '/').split('/')
            # Take last 2 parts: e.g., ['C:', 'xampp', 'htdocs', 'tg-scraper', 'utils', 'logger.py'] → 'utils/logger.py'
            short_path = '/'.join(parts[-2:]) if len(parts) >= 2 else parts[-1]

        # === ALWAYS INCLUDE SOURCE INFO ===
        source_info = f" | {short_path}:{record.lineno}"

        # === FINAL TEXT OUTPUT ===
        if extra_str:
            return f"{levelname}: [{utc_timestamp}] {message} | {extra_str}{source_info}"
        else:
            return f"{levelname}: [{utc_timestamp}] {message}{source_info}"


class LogSection:
    """
    Context manager for formatted log sections.
    """
    def __init__(self, logger: 'Logger', title: str):
        self.logger = logger
        self.title = title
        self.start_time = None

    def _log_header(self):
        """Log the section header with timestamp"""
        self.start_time = datetime.now(timezone.utc)
        # Modified to match the required format
        formatted_header = f"INFO: [{self.start_time.strftime('%Y-%m-%d %H:%M:%S UTC')}] ============== {self.title} =============="
        # Use _log_message to bypass the normal formatting
        self.logger._log_message(logging.INFO, formatted_header)

    def __enter__(self) -> 'LogSection':
        # Check if there's an active section in thread-local storage
        if hasattr(_thread_local, 'active_section') and _thread_local.active_section:
            # Close the previous section before starting a new one
            _thread_local.active_section.__exit__(None, None, None)
        
        self._log_header()
        # Store this section as the active section in thread-local storage
        _thread_local.active_section = self
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Remove this section from thread-local storage
        if hasattr(_thread_local, 'active_section') and _thread_local.active_section == self:
            _thread_local.active_section = None

    def log_step(self, message: str, **kwargs):
        """
        Log an indented step within the section.

        Args:
            message: Step description
            **kwargs: Additional key-value pairs to include in the log
        """
        # Format with the required format
        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
        formatted_step = f"INFO: [{timestamp}] {message}"
        
        # Add any additional key-value pairs
        if kwargs:
            extra_parts = [f"{key}: {value}" for key, value in kwargs.items()]
            formatted_step += " - " + ", ".join(extra_parts)
            
        # Use _log_message to bypass the normal formatting
        self.logger._log_message(logging.INFO, formatted_step)

# --- 2. Configure Root Logger ---
logging.basicConfig(level=numeric_level, handlers=[], force=True)

root_logger = logging.getLogger()
root_logger.setLevel(numeric_level)

stdout_handler = logging.StreamHandler(sys.stdout)
stdout_handler.setFormatter(UnifiedFormatter())
root_logger.addHandler(stdout_handler)


# --- 3. Wrapper Class (Your Logger API) ---
class Logger:
    def __init__(self, source: str):
        self.logger = logging.getLogger(source)
        self.logger.setLevel(numeric_level)
        self.context = {}

    def _log_message(self, level: int, message: str):
        """Log a message directly without additional formatting"""
        # Create a log record with a special flag to bypass formatting
        record = self.logger.makeRecord(
            self.logger.name, 
            level, 
            "(unknown file)", 
            0, 
            message, 
            (), 
            None
        )
        # Add a flag to indicate this message should bypass formatting
        record.bypass_formatting = True
        self.logger.handle(record)

    def with_context(self, **kwargs):
        new_logger = Logger(self.logger.name)
        new_logger.context = {**self.context, **kwargs}
        return new_logger

    def _format_message(self, message: str, **kwargs):
        # Format with the required format
        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
        formatted_message = f"INFO: [{timestamp}] {message}"
        
        # Add any additional key-value pairs
        if kwargs or self.context:
            # Merge context and kwargs, with kwargs taking precedence
            all_kwargs = {**self.context, **kwargs}
            if all_kwargs:
                extra_parts = [f"{key}: {value}" for key, value in all_kwargs.items() if value is not None]
                if extra_parts:
                    formatted_message += " - " + ", ".join(extra_parts)
        
        return formatted_message

    def info(self, message: str, **kwargs: Any) -> None:
        if self.logger.isEnabledFor(logging.INFO):
            formatted_message = self._format_message(message, **kwargs)
            self._log_message(logging.INFO, formatted_message)

    def error(self, message: str, **kwargs: Any) -> None:
        if self.logger.isEnabledFor(logging.ERROR):
            timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
            formatted_message = f"ERROR: [{timestamp}] {message}"
            
            # Add any additional key-value pairs
            if kwargs or self.context:
                # Merge context and kwargs, with kwargs taking precedence
                all_kwargs = {**self.context, **kwargs}
                if all_kwargs:
                    extra_parts = [f"{key}: {value}" for key, value in all_kwargs.items() if value is not None]
                    if extra_parts:
                        formatted_message += " - " + ", ".join(extra_parts)
            
            self._log_message(logging.ERROR, formatted_message)

    def warning(self, message: str, **kwargs: Any) -> None:
        if self.logger.isEnabledFor(logging.WARNING):
            timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
            formatted_message = f"WARNING: [{timestamp}] {message}"
            
            # Add any additional key-value pairs
            if kwargs or self.context:
                # Merge context and kwargs, with kwargs taking precedence
                all_kwargs = {**self.context, **kwargs}
                if all_kwargs:
                    extra_parts = [f"{key}: {value}" for key, value in all_kwargs.items() if value is not None]
                    if extra_parts:
                        formatted_message += " - " + ", ".join(extra_parts)
            
            self._log_message(logging.WARNING, formatted_message)

    def debug(self, message: str, **kwargs: Any) -> None:
        if self.logger.isEnabledFor(logging.DEBUG):
            timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
            formatted_message = f"DEBUG: [{timestamp}] {message}"
            
            # Add any additional key-value pairs
            if kwargs or self.context:
                # Merge context and kwargs, with kwargs taking precedence
                all_kwargs = {**self.context, **kwargs}
                if all_kwargs:
                    extra_parts = [f"{key}: {value}" for key, value in all_kwargs.items() if value is not None]
                    if extra_parts:
                        formatted_message += " - " + ", ".join(extra_parts)
            
            self._log_message(logging.DEBUG, formatted_message)

    def _debug_json_no_format(self, message: str, **kwargs: Any) -> None:
        """Output raw JSON without additional formatting"""
        log_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "level": "DEBUG",
            "message": message,
            "logger": self.logger.name,
            **self.context,
            **kwargs
        }
        # Output JSON directly to stdout
        print(json.dumps(log_data, ensure_ascii=False))

    def _debug_json_with_source(self, message: str, **kwargs: Any) -> None:
        """Output raw JSON with source information included"""
        # Get the caller's frame info for source information
        import inspect
        frame = inspect.currentframe().f_back.f_back  # Go back two frames to get the original caller
        filename = frame.f_code.co_filename if frame else ""
        lineno = frame.f_lineno if frame else 0
        
        # Shorten path to last 2 components
        if filename:
            parts = filename.replace('\\', '/').split('/')
            short_path = '/'.join(parts[-2:]) if len(parts) >= 2 else parts[-1]
        else:
            short_path = "<unknown>"
            
        source_info = f"{short_path}:{lineno}"
        
        log_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "level": "DEBUG",
            "message": message,
            "logger": self.logger.name,
            "source": source_info,
            **self.context,
            **kwargs
        }
        
        # Output JSON directly to stdout
        print(json.dumps(log_data, ensure_ascii=False))

    def debug_json(self, message: str, **kwargs: Any) -> None:
        """Output JSON only at debug level with source info included"""
        if not self.logger.isEnabledFor(logging.DEBUG):
            return
            
        # Get the caller's frame info for source information
        import inspect
        frame = inspect.currentframe().f_back
        filename = frame.f_code.co_filename if frame else ""
        lineno = frame.f_lineno if frame else 0
        
        # Shorten path to last 2 components
        if filename:
            parts = filename.replace('\\', '/').split('/')
            short_path = '/'.join(parts[-2:]) if len(parts) >= 2 else parts[-1]
        else:
            short_path = "<unknown>"
            
        source_info = f"{short_path}:{lineno}"
        
        log_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "level": "DEBUG",
            "message": message,
            "logger": self.logger.name,
            "source": source_info,
            **self.context,
            **kwargs
        }
        
        # Output JSON directly to stdout
        print(json.dumps(log_data, ensure_ascii=False))

    def begin_section(self, section_title: str) -> 'LogSection':
        """
        Begin a new formatted log section.

        Args:
            section_title: Title of the section

        Returns:
            LogSection: Context manager for the section
        """
        return LogSection(self, section_title)

    def log_step(self, message: str, **kwargs) -> None:
        """
        Log a standalone step with the required formatting.

        Args:
            message: Step description
            **kwargs: Additional key-value pairs to include in the log
        """
        # Format with the required format
        timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
        formatted_step = f"INFO: [{timestamp}] {message}"
        
        # Add any additional key-value pairs
        if kwargs:
            extra_parts = [f"{key}: {value}" for key, value in kwargs.items()]
            formatted_step += " - " + ", ".join(extra_parts)
            
        self._log_message(logging.INFO, formatted_step)