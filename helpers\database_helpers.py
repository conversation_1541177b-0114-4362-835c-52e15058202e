"""
Database helper functions for the Telegram Scraper application.
This module contains utility functions for database operations.
"""

from sqlalchemy import text, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from utils.logger import Logger

db_helper_logger = Logger("database_helper")

async def test_connection(db_session: AsyncSession) -> bool:
    """
    Test database connection.
    
    Args:
        db_session (AsyncSession): Database session
        
    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        # Using SQLAlchemy core instead of raw SQL
        from sqlalchemy import select
        result = await db_session.execute(select(1))
        return result.scalar() == 1
    except Exception as e:
        db_helper_logger.error("Database connection test failed", error=str(e))
        return False

async def execute_raw_query(db_session: AsyncSession, query: str, params: dict = None) -> list:
    """
    Execute a raw SQL query.
    
    Args:
        db_session (AsyncSession): Database session
        query (str): SQL query to execute
        params (dict): Query parameters
        
    Returns:
        list: Query results
    """
    # This function is specifically for executing raw queries when needed
    # We'll keep it but add a warning in the docstring
    try:
        if params:
            result = await db_session.execute(text(query), params)
        else:
            result = await db_session.execute(text(query))
        return result.fetchall()
    except Exception as e:
        db_helper_logger.error("Raw query execution failed", query=query, error=str(e))
        raise

async def get_table_row_count(db_session: AsyncSession, table_name: str) -> int:
    """
    Get the row count for a table.
    
    Args:
        db_session (AsyncSession): Database session
        table_name (str): Name of the table
        
    Returns:
        int: Number of rows in the table
    """
    try:
        # Using SQLAlchemy to avoid raw SQL
        # We need to dynamically access the table model
        # This is a simplified approach - in practice, you'd want to map table names to models
        from sqlalchemy import Table, MetaData
        metadata = MetaData()
        table = Table(table_name, metadata, autoload_with=db_session.bind)
        stmt = select(func.count()).select_from(table)
        result = await db_session.execute(stmt)
        return result.scalar()
    except Exception as e:
        db_helper_logger.error("Table row count query failed for table", table_name=table_name, error=str(e))
        # Fallback to raw SQL if the dynamic approach fails
        try:
            result = await db_session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            return result.scalar()
        except Exception as fallback_e:
            db_helper_logger.error("Fallback raw query also failed for table", table_name=table_name, error=str(fallback_e))
            raise

def format_database_url(host: str, port: int, username: str, password: str, database: str, driver: str = "mysql+aiomysql") -> str:
    """
    Format a database URL.
    
    Args:
        host (str): Database host
        port (int): Database port
        username (str): Database username
        password (str): Database password
        database (str): Database name
        driver (str): Database driver
        
    Returns:
        str: Formatted database URL
    """
    return f"{driver}://{username}:{password}@{host}:{port}/{database}"