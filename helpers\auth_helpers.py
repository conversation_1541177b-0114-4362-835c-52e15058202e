"""
Authentication helper functions for the Telegram Scraper application.
This module contains utility functions for authentication operations.
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import J<PERSON><PERSON>rror, jwt
from passlib.context import CryptContext
from utils.config_loader import ConfigLoader

# Load configuration
config = ConfigLoader.load_config()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT configuration
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a plain password against a hashed password.
    
    Args:
        plain_password (str): Plain text password
        hashed_password (str): Hashed password
        
    Returns:
        bool: True if password matches, False otherwise
    """
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """
    Hash a plain password.
    
    Args:
        password (str): Plain text password
        
    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.
    
    Args:
        data (dict): Data to encode in the token
        expires_delta (Optional[timedelta]): Token expiration time
        
    Returns:
        str: JWT access token
    """
    # Use a fixed secret key since we're not using the config value
    SECRET_KEY = "your-secret-key"
    
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """
    Verify a JWT token and return the payload.
    
    Args:
        token (str): JWT token to verify
        
    Returns:
        Optional[dict]: Decoded payload if valid, None otherwise
    """
    # Use a fixed secret key since we're not using the config value
    SECRET_KEY = "your-secret-key"
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None

def generate_otp() -> str:
    """
    Generate a random OTP code.
    
    Returns:
        str: 5-digit OTP code
    """
    import random
    return str(random.randint(10000, 99999))

def validate_otp_format(otp: str) -> bool:
    """
    Validate OTP format.
    
    Args:
        otp (str): OTP code to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    return otp.isdigit() and len(otp) == 5