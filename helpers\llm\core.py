"""
LLM Core Helper Module

This module provides core functionality for interacting with Large Language Models
using the Langchain framework.
"""

from typing import Optional, Dict, Any, List
from langchain_openai import <PERSON>tOpenAI
from langchain_anthropic import <PERSON>tAnthropic
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.prompts import <PERSON>t<PERSON>romptTemplate
from langchain_core.runnables import RunnableSequence
from utils.logger import Logger
import asyncio

# Initialize logger
llm_logger = Logger("llm_core")

class LLMProvider:
    """Enum-like class for LLM providers"""
    OPENAI = 0
    ANTHROPIC = 1
    # Add more providers as needed

class LLMHelper:
    """Helper class for LLM operations using Langchain"""
    
    def __init__(self, provider: int, api_key: str, model_name: str = None, 
                 temperature: float = 0.7, max_tokens: int = 1000, timeout: int = 240):
        """
        Initialize the LLM helper.
        
        Args:
            provider: LLM provider (LLMProvider.OPENAI, LLMProvider.ANTHROPIC, etc.)
            api_key: API key for the LLM provider
            model_name: Specific model name to use
            temperature: Temperature setting for the LLM
            max_tokens: Maximum tokens for the response
            timeout: Timeout in seconds for API calls
        """
        self.provider = provider
        self.api_key = api_key
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.timeout = timeout
        self.model_name = model_name
        self.llm = self._initialize_llm()
        
    def _initialize_llm(self):
        """Initialize the LLM based on the provider."""
        try:
            if self.provider == LLMProvider.OPENAI:
                # Default model if not specified
                if not self.model_name:
                    self.model_name = "gpt-3.5-turbo"
                
                llm = ChatOpenAI(
                    model=self.model_name,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    timeout=self.timeout,
                    openai_api_key=self.api_key
                )
                llm_logger.info("Initialized OpenAI LLM with model", model_name=self.model_name)
                return llm
                
            elif self.provider == LLMProvider.ANTHROPIC:
                # Default model if not specified
                if not self.model_name:
                    self.model_name = "claude-3-haiku-20240307"
                
                llm = ChatAnthropic(
                    model=self.model_name,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    timeout=self.timeout,
                    anthropic_api_key=self.api_key
                )
                llm_logger.info("Initialized Anthropic LLM with model", model_name=self.model_name)
                return llm
                
            else:
                raise ValueError(f"Unsupported LLM provider: {self.provider}")
                
        except Exception as e:
            llm_logger.error("Failed to initialize LLM", error=str(e))
            raise
            
    async def generate_response(self, system_prompt: str, user_prompt: str, 
                               context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a response using the LLM.
        
        Args:
            system_prompt: System prompt for the LLM
            user_prompt: User prompt for the LLM
            context: Additional context to include in the prompt
            
        Returns:
            str: Generated response from the LLM
        """
        try:
            # Format the user prompt with context if provided
            if context:
                try:
                    user_prompt = user_prompt.format(**context)
                except KeyError as e:
                    llm_logger.warning("Missing context key in user prompt", key=str(e))
            
            # Create messages
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # Generate response
            response = await self.llm.ainvoke(messages)
            
            # Extract the content from the response
            content = ""
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
                
            # Ensure the response is complete and coherent
            content = self._ensure_complete_response(content)
            
            return content
                
        except Exception as e:
            llm_logger.error("Error generating LLM response", error=str(e))
            raise
            
    async def generate_response_with_history(self, messages: List[Dict[str, str]]) -> str:
        """
        Generate a response using the LLM with message history.
        
        Args:
            messages: List of messages with 'role' and 'content' keys
            
        Returns:
            str: Generated response from the LLM
        """
        try:
            # Convert messages to Langchain format
            langchain_messages = []
            for msg in messages:
                if msg['role'] == 'system':
                    langchain_messages.append(SystemMessage(content=msg['content']))
                elif msg['role'] == 'user':
                    langchain_messages.append(HumanMessage(content=msg['content']))
                elif msg['role'] == 'assistant':
                    langchain_messages.append(AIMessage(content=msg['content']))
            
            # Generate response
            response = await self.llm.ainvoke(langchain_messages)
            
            # Extract the content from the response
            content = ""
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
                
            # Ensure the response is complete and coherent
            content = self._ensure_complete_response(content)
            
            return content
                
        except Exception as e:
            llm_logger.error("Error generating LLM response with history", error=str(e))
            raise
            
    async def generate_response_for_telegram(self, system_prompt: str, user_prompt: str,
                                           message_context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a response specifically for Telegram messages.
        
        Args:
            system_prompt: System prompt for the LLM
            user_prompt: User prompt for the LLM
            message_context: Additional context including message_thread_id and other Telegram-specific data
            
        Returns:
            str: Generated response from the LLM
        """
        try:
            # Format the user prompt with message context if provided
            if message_context:
                try:
                    user_prompt = user_prompt.format(**message_context)
                except KeyError as e:
                    llm_logger.warning("Missing context key in user prompt", key=str(e))
            
            # Create messages
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # Generate response
            response = await self.llm.ainvoke(messages)
            
            # Extract the content from the response
            content = ""
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
                
            # Ensure the response is complete and coherent
            content = self._ensure_complete_response(content)
            
            return content
                
        except Exception as e:
            llm_logger.error("Error generating Telegram-specific LLM response", error=str(e))
            raise
            
    async def generate_crypto_analysis(self, market_data: Dict[str, Any], 
                                     analysis_type: str = "general") -> Dict[str, Any]:
        """
        Generate crypto market analysis using the LLM.
        
        Args:
            market_data: Dictionary containing market data to analyze
            analysis_type: Type of analysis to perform ("general", "trading_signal", "sentiment")
            
        Returns:
            Dict[str, Any]: Analysis results including signals, sentiment, and recommendations
        """
        try:
            # Define system prompts based on analysis type
            system_prompts = {
                "general": "You are a cryptocurrency market analyst. Analyze the provided market data and provide insights.",
                "trading_signal": "You are a professional crypto trader. Analyze the market data and provide actionable trading signals.",
                "sentiment": "You are a sentiment analysis expert. Analyze the market data and determine overall market sentiment."
            }
            
            # Define user prompts based on analysis type
            user_prompts = {
                "general": "Analyze the following cryptocurrency market data and provide a comprehensive analysis:\n{market_data}",
                "trading_signal": "Based on the following market data, provide specific trading signals (BUY, SELL, HOLD) with confidence levels:\n{market_data}",
                "sentiment": "Analyze the sentiment of the following market data and provide a sentiment score (-1 to 1) with explanation:\n{market_data}"
            }
            
            # Format market data as string
            market_data_str = "\n".join([f"{key}: {value}" for key, value in market_data.items()])
            
            # Generate response
            response = await self.generate_response(
                system_prompt=system_prompts.get(analysis_type, system_prompts["general"]),
                user_prompt=user_prompts.get(analysis_type, user_prompts["general"]),
                context={"market_data": market_data_str}
            )
            
            return {
                "analysis_type": analysis_type,
                "response": response,
                "token_count": self.get_token_count(response)
            }
            
        except Exception as e:
            llm_logger.error("Error generating crypto analysis", error=str(e))
            raise
            
    async def generate_unique_content(self, topic: str, content_type: str = "article", 
                                    style_guide: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate unique content on a given topic.
        
        Args:
            topic: Topic to generate content about
            content_type: Type of content to generate ("article", "social_post", "report", "analysis")
            style_guide: Optional style guide for content generation
            
        Returns:
            str: Generated unique content
        """
        try:
            # Define system prompts based on content type
            system_prompts = {
                "article": "You are a professional content writer. Create a well-structured, engaging article.",
                "social_post": "You are a social media expert. Create a concise, engaging social media post.",
                "report": "You are a technical writer. Create a detailed, factual report.",
                "analysis": "You are an analyst. Create a comprehensive analysis with data-driven insights."
            }
            
            # Define user prompts based on content type
            user_prompts = {
                "article": "Write a comprehensive article about {topic}. Include an introduction, main points, and conclusion.",
                "social_post": "Create an engaging social media post about {topic} in 280 characters or less.",
                "report": "Generate a detailed report about {topic}. Include key facts, data, and findings.",
                "analysis": "Provide a comprehensive analysis of {topic}. Include key insights, trends, and implications."
            }
            
            # Add style guide to context if provided
            context = {"topic": topic}
            if style_guide:
                context["style_guide"] = "\n".join([f"{key}: {value}" for key, value in style_guide.items()])
                user_prompts[content_type] += "\n\nStyle Guide:\n{style_guide}"
            
            # Generate response
            response = await self.generate_response(
                system_prompt=system_prompts.get(content_type, system_prompts["article"]),
                user_prompt=user_prompts.get(content_type, user_prompts["article"]),
                context=context
            )
            
            # Ensure the response is complete and coherent
            response = self._ensure_complete_response(response)
            
            return response
            
        except Exception as e:
            llm_logger.error("Error generating unique content", error=str(e))
            raise
            
    def _ensure_complete_response(self, response: str) -> str:
        """
        Ensure the response is complete and coherent, not cut off mid-sentence.
        
        Args:
            response: LLM response that may be incomplete
            
        Returns:
            str: Complete and coherent response
        """
        try:
            # If response is empty, return as is
            if not response or not response.strip():
                return response
                
            # Trim whitespace
            response = response.strip()
            
            # Check for placeholder content and return empty string to indicate failure
            placeholder_indicators = [
                'XXX',
                'placeholder',
                'Note: This is a placeholder',
                'did not provide specific messages',
                'generic summary',
                'no specific information',
                'no meaningful content'
            ]
            
            # Safely check for placeholder content, handling None responses
            is_placeholder = False
            if response:
                is_placeholder = any(indicator.lower() in response.lower() for indicator in placeholder_indicators)
            if is_placeholder:
                return ""  # Return empty response which will be marked as failed
            
            # Check if the response ends with common sentence-ending punctuation
            if response.endswith(('.', '!', '?', '"', "'")):
                # Response appears to be complete
                return response
                
            # If the response ends with a comma, semicolon, colon, or dash, 
            # it's likely incomplete
            if response.endswith((',', ';', ':', '-', '–')):
                # Try to find the last complete sentence
                last_sentence_end = max(
                    response.rfind('.'),
                    response.rfind('!'),
                    response.rfind('?')
                )
                
                # If we found a sentence ending, truncate to that point
                if last_sentence_end > 0:
                    return response[:last_sentence_end + 1].strip()
                    
            # If we can't find a good truncation point, add an ellipsis to indicate incompleteness
            # but only if the response is long enough to be meaningful
            if len(response) > 50:
                # Check if the response ends with a word (not cut off mid-word)
                if response[-1].isalpha():
                    # Find the last space to avoid cutting off mid-word
                    last_space = response.rfind(' ')
                    if last_space > len(response) * 0.7:  # If it's in the last 30%
                        response = response[:last_space].strip()
                        
            return response
            
        except Exception as e:
            llm_logger.warning("Error ensuring complete response", error=str(e))
            # Return original response if we can't process it
            return response
            
    def get_token_count(self, text: str) -> int:
        """
        Get the approximate token count for a text.
        
        Args:
            text: Text to count tokens for
            
        Returns:
            int: Approximate token count
        """
        try:
            # For OpenAI models, we can use tiktoken
            if self.provider == LLMProvider.OPENAI:
                import tiktoken
                encoding = tiktoken.encoding_for_model(self.model_name)
                return len(encoding.encode(text))
            else:
                # For other models, use a rough estimate
                # Average of 4 characters per token
                return len(text) // 4
        except Exception as e:
            llm_logger.warning("Error counting tokens", error=str(e))
            # Fallback to rough estimate
            return len(text) // 4