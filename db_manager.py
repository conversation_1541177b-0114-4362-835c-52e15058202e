#!/usr/bin/env python3
"""
Database Manager

This script provides a comprehensive interface for managing the database including:
1. Setting up the database (create database and user if needed)
2. Running database migrations
3. Fixing common migration issues
4. Checking migration status
5. Creating new migrations

Usage:
    python db_manager.py setup              # Set up database and run migrations
    python db_manager.py migrate            # Run migrations
    python db_manager.py status             # Show current migration status
    python db_manager.py fix                # Fix common migration issues
    python db_manager.py stamp              # Stamp database with latest revision
    python db_manager.py create "message"   # Create new migration
"""

import os
import sys
import argparse
import pymysql
import subprocess
from alembic.config import Config
from alembic import command
from alembic.script import ScriptDirectory
from alembic.runtime.migration import MigrationContext
from sqlalchemy import create_engine, select, text
from utils.config_loader import ConfigLoader
from utils.logger import Logger

# Initialize logger
db_logger = Logger("db_manager")

def get_mysql_connection(host, port, user, password):
    """Create a MySQL connection."""
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        return connection
    except Exception as e:
        db_logger.error("Failed to connect to MySQL", error=str(e))
        return None

def create_database_and_user(connection, db_name, db_user, db_password):
    """Create database and user with necessary privileges."""
    try:
        with connection.cursor() as cursor:
            # Create database if it doesn't exist
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{db_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            
            # Create user if it doesn't exist
            cursor.execute(f"CREATE USER IF NOT EXISTS '{db_user}'@'localhost' IDENTIFIED BY '{db_password}'")
            
            # Grant privileges
            cursor.execute(f"GRANT ALL PRIVILEGES ON `{db_name}`.* TO '{db_user}'@'localhost'")
            
            # Flush privileges
            cursor.execute("FLUSH PRIVILEGES")
            
        connection.commit()
        db_logger.info("Database and user created successfully", database=db_name, user=db_user)
        return True
    except Exception as e:
        db_logger.error("Failed to create database and user", error=str(e))
        return False

def update_config_files(db_name, db_user, db_password):
    """Update configuration files with database credentials."""
    try:
        # Load current config
        config = ConfigLoader.load_config()
        
        # Update database configuration to use the new structure
        config['database'] = db_name
        config['username'] = db_user
        config['password'] = db_password
        
        # Write updated config back to file
        with open('config.json', 'w') as f:
            import json
            json.dump(config, f, indent=2)
            
        db_logger.info("Configuration files updated successfully")
        return True
    except Exception as e:
        db_logger.error("Failed to update configuration files", error=str(e))
        return False

def get_alembic_config():
    """Get Alembic configuration."""
    # Get the directory where this script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Path to alembic.ini
    alembic_ini_path = os.path.join(script_dir, "alembic.ini")
    
    # Check if alembic.ini exists
    if not os.path.exists(alembic_ini_path):
        db_logger.error("alembic.ini not found", path=alembic_ini_path)
        raise FileNotFoundError(f"Alembic configuration file not found at {alembic_ini_path}")
    
    # Create Alembic config
    alembic_cfg = Config(alembic_ini_path)
    
    # Set the script location if not already set
    if not alembic_cfg.get_main_option("script_location"):
        migrations_dir = os.path.join(script_dir, "migrations")
        alembic_cfg.set_main_option("script_location", migrations_dir)
    
    return alembic_cfg

def get_database_url():
    """Get database URL from configuration."""
    try:
        config = ConfigLoader.load_config()
        
        # Use the new config structure
        db_url = f"mysql+pymysql://{config['username']}:{config['password']}@{config['host']}:3306/{config['database']}"
        return db_url
    except Exception as e:
        db_logger.error("Failed to load database configuration", error=str(e))
        raise

def check_database_connection(db_url):
    """Check if database connection is possible."""
    try:
        engine = create_engine(db_url)
        with engine.connect() as connection:
            # Using SQLAlchemy instead of raw SQL
            result = connection.execute(select(1))
            return result.scalar() == 1
    except Exception as e:
        db_logger.error("Database connection failed", error=str(e))
        return False

def check_database_exists(db_name, db_user, db_password, db_host):
    """Check if the database already exists."""
    try:
        db_url = f"mysql+pymysql://{db_user}:{db_password}@{db_host}:3306/{db_name}"
        engine = create_engine(db_url)
        with engine.connect() as connection:
            # If we can connect, the database exists
            return True
    except Exception as e:
        # If we can't connect, the database might not exist
        db_logger.info("Database connection failed, assuming database doesn't exist", error=str(e))
        return False

def show_current_revision(db_url):
    """Show the current database revision."""
    try:
        engine = create_engine(db_url)
        with engine.connect() as connection:
            context = MigrationContext.configure(connection)
            current_rev = context.get_current_revision()
            return current_rev
    except Exception as e:
        db_logger.error("Failed to get current revision", error=str(e))
        return None

def upgrade_to_latest(alembic_cfg, db_url):
    """Upgrade database to the latest revision."""
    db_logger.info("Upgrading database to latest revision")
    try:
        # Check database connection first
        if not check_database_connection(db_url):
            db_logger.error("Cannot connect to database. Please check your configuration.")
            return False
            
        # Run the upgrade
        command.upgrade(alembic_cfg, "head")
        db_logger.info("Database successfully upgraded to latest revision")
        return True
    except Exception as e:
        # Check if it's a table already exists error
        error_msg = str(e).lower()
        if "table" in error_msg and "already exists" in error_msg:
            db_logger.warning("Table already exists. This may be due to a previous partial migration.")
            db_logger.info("Attempting to fix by stamping database...")
            try:
                # Stamp the database with the current revision
                current_rev = show_current_revision(db_url)
                if current_rev:
                    command.stamp(alembic_cfg, current_rev)
                    db_logger.info("Database stamped with current revision", revision=current_rev)
                else:
                    command.stamp(alembic_cfg, "head")
                    db_logger.info("Database stamped with head revision")
                return True
            except Exception as stamp_error:
                db_logger.error("Failed to stamp database", error=str(stamp_error))
                return False
        else:
            db_logger.error("Failed to upgrade database", error=str(e))
            return False

def stamp_database(alembic_cfg, db_url):
    """Stamp the database with the latest revision."""
    db_logger.info("Stamping database with latest revision")
    try:
        # Check database connection first
        if not check_database_connection(db_url):
            db_logger.error("Cannot connect to database. Please check your configuration.")
            return False
            
        # Stamp the database
        command.stamp(alembic_cfg, "head")
        db_logger.info("Database successfully stamped with latest revision")
        return True
    except Exception as e:
        db_logger.error("Failed to stamp database", error=str(e))
        return False

def fix_migration_issues(alembic_cfg, db_url):
    """Fix common migration issues."""
    db_logger.info("Starting migration fix process")
    
    try:
        # Check database connection first
        if not check_database_connection(db_url):
            db_logger.error("Cannot connect to database. Please check your configuration.")
            return False
            
        # Check for multiple heads
        script = ScriptDirectory.from_config(alembic_cfg)
        heads = script.get_heads()
        
        if len(heads) > 1:
            db_logger.warning("Multiple heads detected", heads=heads)
            db_logger.info("Attempting to resolve by stamping with one of the heads")
            # Stamp the database with the first head revision
            command.stamp(alembic_cfg, heads[0])
            db_logger.info("Database stamped with revision", revision=heads[0])
        else:
            # Stamp the database with head revision to mark as up-to-date
            if stamp_database(alembic_cfg, db_url):
                db_logger.info("Successfully fixed migration state.")
                return True
            else:
                db_logger.error("Failed to fix migration state.")
                return False
                
        return True
            
    except Exception as e:
        db_logger.error("Unexpected error during migration fix process", error=str(e))
        return False

def show_status(alembic_cfg, db_url):
    """Show migration status."""
    db_logger.info("Checking migration status")
    
    try:
        # Check database connection first
        if not check_database_connection(db_url):
            db_logger.error("Cannot connect to database. Please check your configuration.")
            return False
            
        # Get current revision
        current_rev = show_current_revision(db_url)
        
        if current_rev:
            db_logger.info("Current database revision", revision=current_rev)
        else:
            db_logger.info("Database is at the base revision (no migrations applied)")
            
        # Check for multiple heads
        script = ScriptDirectory.from_config(alembic_cfg)
        heads = script.get_heads()
        
        if len(heads) > 1:
            db_logger.error("Multiple heads detected", heads=heads)
            db_logger.error("Migration chain is not linear. Please resolve before proceeding.")
            print(f"ERROR: Multiple heads detected: {heads}")
            print("Migration chain is not linear. Please resolve before proceeding.")
            return False
        else:
            db_logger.info("Migration chain is linear. No multiple heads detected.")
            print("Migration chain is linear. No multiple heads detected.")
            
        # Show available revisions
        db_logger.info("Available revisions:")
        for revision in script.walk_revisions():
            db_logger.info("Database revision info", revision=revision.revision, doc=revision.doc)
            
        return True
    except Exception as e:
        db_logger.error("Failed to show migration status", error=str(e))
        return False

def create_register_tg_entities_if_missing(db_url):
    """Create register_tg_entities table if it's missing."""
    try:
        db_logger.info("Checking if register_tg_entities table exists")
        
        # Create database connection
        engine = create_engine(db_url)
        
        # Check if register_tg_entities table exists
        with engine.connect() as conn:
            result = conn.execute(select(1).select_from(text("information_schema.TABLES")).where(
                text("TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'register_tg_entities'")))
            
            if result.fetchone():
                db_logger.info("register_tg_entities table already exists")
                return True
            
            db_logger.info("register_tg_entities table missing, creating it")
            
            # Check if project_id column exists in register table and has unique constraint
            result = conn.execute(text("""
                SELECT COLUMN_NAME, CONSTRAINT_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'register' 
                AND COLUMN_NAME = 'project_id'
            """))
            
            rows = result.fetchall()
            has_project_id_constraint = any('unique' in str(row[1]).lower() or 'project_id' in str(row[1]).lower() for row in rows)
            
            # Add unique constraint to project_id column if it doesn't exist
            if not has_project_id_constraint:
                try:
                    conn.execute(text("ALTER TABLE register ADD UNIQUE (project_id)"))
                    db_logger.info("Added unique constraint to project_id column")
                except Exception as e:
                    db_logger.info("Unique constraint may already exist or error", error=str(e))
            
            # Create the register_tg_entities table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS register_tg_entities (
                    id INTEGER NOT NULL AUTO_INCREMENT,
                    project_id INTEGER NOT NULL,
                    tg_entity_id INTEGER NOT NULL,
                    PRIMARY KEY (id)
                )
            """))
            db_logger.info("Created register_tg_entities table")
            
            # Add indexes for better performance
            try:
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_register_tg_entities_project_id ON register_tg_entities (project_id)"))
                db_logger.info("Created index on project_id")
            except Exception as e:
                db_logger.info("Index on project_id may already exist", error=str(e))
                
            try:
                conn.execute(text("CREATE INDEX IF NOT EXISTS idx_register_tg_entities_tg_entity_id ON register_tg_entities (tg_entity_id)"))
                db_logger.info("Created index on tg_entity_id")
            except Exception as e:
                db_logger.info("Index on tg_entity_id may already exist", error=str(e))
            
            # Add foreign key constraints if possible
            try:
                conn.execute(text("ALTER TABLE register_tg_entities ADD CONSTRAINT fk_register_tg_entities_project_id FOREIGN KEY (project_id) REFERENCES register (project_id)"))
                db_logger.info("Added foreign key constraint for project_id")
            except Exception as e:
                db_logger.info("Foreign key constraint for project_id may already exist or error", error=str(e))
                
            try:
                conn.execute(text("ALTER TABLE register_tg_entities ADD CONSTRAINT fk_register_tg_entities_tg_entity_id FOREIGN KEY (tg_entity_id) REFERENCES tg_entities (id)"))
                db_logger.info("Added foreign key constraint for tg_entity_id")
            except Exception as e:
                db_logger.info("Foreign key constraint for tg_entity_id may already exist or error", error=str(e))
            
            conn.commit()
            
        db_logger.info("Table 'register_tg_entities' created successfully!")
        return True
        
    except Exception as e:
        db_logger.error("Error creating register_tg_entities table", error=str(e))
        return False

def setup_database():
    """Set up the database if it doesn't exist."""
    db_logger.info("Starting database setup process")
    
    # Load configuration
    try:
        config = ConfigLoader.load_config()
        
        # Use the new config structure
        db_name = config['database']
        db_user = config['username']
        db_password = config['password']
        db_host = config['host']
        db_port = 3306  # Default MySQL port
    except Exception as e:
        db_logger.error("Failed to load configuration", error=str(e))
        return False
    
    # Check if database already exists
    if check_database_exists(db_name, db_user, db_password, db_host):
        db_logger.info("Database already exists, skipping setup")
        return True
    
    # Get MySQL root credentials
    print("Database not found. Please enter MySQL root credentials to create database and user:")
    root_user = input("MySQL root username (default: root): ").strip() or "root"
    root_password = input("MySQL root password: ").strip()
    
    if not root_password:
        print("Root password is required!")
        return False
    
    # Connect to MySQL as root
    connection = get_mysql_connection(db_host, db_port, root_user, root_password)
    if not connection:
        print("Failed to connect to MySQL with root credentials!")
        return False
    
    try:
        # Create database and user
        if not create_database_and_user(connection, db_name, db_user, db_password):
            print("Failed to create database and user!")
            return False
        
        # Update config files
        if not update_config_files(db_name, db_user, db_password):
            print("Failed to update configuration files!")
            return False
            
        print("Database setup completed successfully!")
        return True
    finally:
        connection.close()

def run_migrations():
    """Run database migrations."""
    try:
        db_logger.info("Running database migrations")
        
        # Get Alembic configuration
        alembic_cfg = get_alembic_config()
        
        # Get database URL
        db_url = get_database_url()
        
        # Run migrations
        if upgrade_to_latest(alembic_cfg, db_url):
            # Ensure register_tg_entities table exists
            create_register_tg_entities_if_missing(db_url)
            
            db_logger.info("Database migrations completed successfully")
            return True
        else:
            db_logger.error("Database migrations failed")
            return False

    except Exception as e:
        db_logger.error("Failed to run migrations", error=str(e))
        return False

def create_migration(message: str):
    """Create a new migration file."""
    try:
        alembic_cfg = get_alembic_config()
        command.revision(alembic_cfg, message, autogenerate=True)
        db_logger.info("New migration created successfully")
        return True
    except Exception as e:
        db_logger.error("Failed to create migration", error=str(e))
        return False

def add_indexes_for_performance():
    """Add indexes to tables for performance optimization."""
    db_logger.info("Adding indexes for performance optimization")
    try:
        alembic_cfg = get_alembic_config()
        db_url = get_database_url()
        
        # Check database connection first
        if not check_database_connection(db_url):
            db_logger.error("Cannot connect to database. Please check your configuration.")
            return False
            
        # Apply the specific migration for adding indexes
        command.upgrade(alembic_cfg, "20250909_174709")
        db_logger.info("Indexes added successfully")
        return True
    except Exception as e:
        db_logger.error("Failed to add indexes", error=str(e))
        return False

def main():
    """Main function to handle database management commands."""
    parser = argparse.ArgumentParser(
        description="Database Management Tool",
        epilog="Examples:\n"
               "  python db_manager.py setup     # Set up database and run migrations\n"
               "  python db_manager.py migrate   # Run migrations\n"
               "  python db_manager.py status    # Show current migration status\n"
               "  python db_manager.py fix       # Fix common migration issues\n"
               "  python db_manager.py indexes   # Add indexes for performance optimization\n"
               "  python db_manager.py create \"message\"  # Create new migration"
    )
    
    parser.add_argument(
        "command",
        choices=["setup", "migrate", "status", "fix", "stamp", "create", "indexes"],
        help="Command to execute"
    )
    
    parser.add_argument(
        "message",
        nargs="?",
        help="Message for creating new migration"
    )
    
    args = parser.parse_args()
    
    try:
        if args.command == "setup":
            # Set up database and run migrations
            if not setup_database():
                print("Database setup failed!")
                return 1
            
            if not run_migrations():
                print("Database migrations failed!")
                return 1
            
            db_logger.info("Database setup and migration process completed successfully")
            print("Database setup and migration process completed successfully!")
            return 0
            
        elif args.command == "migrate":
            # Run migrations
            if run_migrations():
                db_logger.info("Database migrations completed successfully")
                print("Database migrations completed successfully!")
                return 0
            else:
                db_logger.error("Database migrations failed")
                print("Database migrations failed.")
                return 1
                
        elif args.command == "status":
            # Show status
            # Get Alembic configuration
            alembic_cfg = get_alembic_config()
            
            # Get database URL
            db_url = get_database_url()
            
            if show_status(alembic_cfg, db_url):
                return 0
            else:
                return 1
                
        elif args.command == "fix":
            # Fix migration issues
            # Get Alembic configuration
            alembic_cfg = get_alembic_config()
            
            # Get database URL
            db_url = get_database_url()
            
            if fix_migration_issues(alembic_cfg, db_url):
                db_logger.info("Migration issues fixed successfully!")
                print("Migration issues fixed successfully!")
                print("You can now run normal migrations with: python db_manager.py migrate")
                return 0
            else:
                db_logger.error("Failed to fix migration issues.")
                print("Failed to fix migration issues.")
                return 1
                
        elif args.command == "stamp":
            # Stamp database
            # Get Alembic configuration
            alembic_cfg = get_alembic_config()
            
            # Get database URL
            db_url = get_database_url()
            
            if stamp_database(alembic_cfg, db_url):
                db_logger.info("Database stamped successfully!")
                print("Database stamped successfully!")
                return 0
            else:
                db_logger.error("Failed to stamp database.")
                print("Failed to stamp database.")
                return 1
                
        elif args.command == "create":
            # Create new migration
            if not args.message:
                print("Message is required for creating new migration!")
                return 1
                
            if create_migration(args.message):
                db_logger.info("Migration created successfully!")
                print("Migration created successfully!")
                return 0
            else:
                db_logger.error("Failed to create migration")
                print("Failed to create migration.")
                return 1
                
        elif args.command == "indexes":
            # Add indexes for performance optimization
            if add_indexes_for_performance():
                db_logger.info("Indexes added successfully!")
                print("Indexes added successfully!")
                return 0
            else:
                db_logger.error("Failed to add indexes")
                print("Failed to add indexes.")
                return 1
                
    except Exception as e:
        db_logger.error("Unexpected error during database management", error=str(e))
        print(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())