"""
Utility functions for LLM operations.
"""

import json
import re
from typing import Dict, Any, Optional, List
from utils.logger import Logger

# Initialize logger
llm_utils_logger = Logger("llm_utils")

def extract_json_from_response(response: str) -> Optional[Dict[str, Any]]:
    """
    Extract JSON data from an LLM response.
    
    Args:
        response: LLM response that may contain JSON
        
    Returns:
        Dict[str, Any]: Extracted JSON data or None if not found
    """
    try:
        # Look for JSON-like patterns in the response
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}|\[[^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)*\]'
        matches = re.findall(json_pattern, response)
        
        # Try to parse each match as JSON
        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue
                
        # If no valid JSON found in patterns, try parsing the entire response
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            pass
            
        return None
    except Exception as e:
        llm_utils_logger.error("Error extracting JSON from response", error=str(e))
        return None

def format_messages_for_llm(messages: List[Dict[str, str]]) -> str:
    """
    Format messages for LLM input.
    
    Args:
        messages: List of messages with 'role' and 'content' keys
        
    Returns:
        str: Formatted messages string
    """
    try:
        formatted = ""
        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            formatted += f"{role.capitalize()}: {content}\n"
        return formatted.strip()
    except Exception as e:
        llm_utils_logger.error("Error formatting messages", error=str(e))
        return ""

def format_telegram_context_for_llm(context: Dict[str, Any]) -> str:
    """
    Format Telegram-specific context for LLM input.
    
    Args:
        context: Dictionary containing Telegram context including message_thread_id
        
    Returns:
        str: Formatted context string
    """
    try:
        formatted_context = []
        
        # Add message thread ID if present
        if 'message_thread_id' in context and context['message_thread_id'] is not None:
            formatted_context.append(f"Message Thread ID: {context['message_thread_id']}")
        
        # Add other context information
        for key, value in context.items():
            if key != 'message_thread_id' and value is not None:
                formatted_context.append(f"{key.replace('_', ' ').title()}: {value}")
        
        return "\n".join(formatted_context)
    except Exception as e:
        llm_utils_logger.error("Error formatting Telegram context", error=str(e))
        return ""

def format_crypto_data_for_llm(crypto_data: Dict[str, Any]) -> str:
    """
    Format crypto data for LLM input.
    
    Args:
        crypto_data: Dictionary containing crypto market data
        
    Returns:
        str: Formatted crypto data string
    """
    try:
        formatted_data = []
        
        # Add key crypto data points
        key_fields = [
            'symbol', 'price', 'volume_24h', 'market_cap', 
            'price_change_percentage_24h', 'sentiment_score'
        ]
        
        for field in key_fields:
            if field in crypto_data and crypto_data[field] is not None:
                formatted_data.append(f"{field.replace('_', ' ').title()}: {crypto_data[field]}")
        
        # Add any additional fields
        for key, value in crypto_data.items():
            if key not in key_fields and value is not None:
                formatted_data.append(f"{key.replace('_', ' ').title()}: {value}")
        
        return "\n".join(formatted_data)
    except Exception as e:
        llm_utils_logger.error("Error formatting crypto data", error=str(e))
        return ""

def truncate_text_to_token_limit(text: str, max_tokens: int, 
                                approximate: bool = True) -> str:
    """
    Truncate text to fit within a token limit.
    
    Args:
        text: Text to truncate
        max_tokens: Maximum number of tokens
        approximate: Whether to use approximate token counting
        
    Returns:
        str: Truncated text
    """
    try:
        if approximate:
            # Rough approximation: 4 characters per token
            max_chars = max_tokens * 4
            if len(text) <= max_chars:
                return text
            # Truncate and add ellipsis
            return text[:max_chars - 3] + "..."
        else:
            # More accurate token counting would require the specific tokenizer
            # This is a simplified version
            return text[:max_tokens * 4] if len(text) > max_tokens * 4 else text
    except Exception as e:
        llm_utils_logger.error("Error truncating text", error=str(e))
        return text

def validate_llm_response(response: str, min_length: int = 1, 
                         max_length: int = 4096) -> bool:
    """
    Validate an LLM response.
    
    Args:
        response: LLM response to validate
        min_length: Minimum response length
        max_length: Maximum response length
        
    Returns:
        bool: Whether the response is valid
    """
    try:
        # Check if response is not empty
        if not response or not response.strip():
            return False
            
        # Check length constraints
        response_length = len(response.strip())
        if response_length < min_length or response_length > max_length:
            return False
            
        # Check for placeholder content
        placeholder_indicators = [
            'XXX',
            'placeholder',
            'Note: This is a placeholder',
            'did not provide specific messages',
            'generic summary',
            'no specific information',
            'no meaningful content'
        ]
        
        # Safely convert response to lowercase, handling None values
        response_lower = response.lower() if response else ""
        for indicator in placeholder_indicators:
            if indicator.lower() in response_lower:
                return False
            
        # Check for common error patterns
        error_patterns = [
            r'error',
            r'exception',
            r'failure',
            r'invalid',
            r'unable to',
            r'couldn\'t',
        ]
        
        for pattern in error_patterns:
            if re.search(pattern, response_lower):
                # Only fail if the pattern is not part of a JSON response
                if not response.strip().startswith('{') and not response.strip().startswith('['):
                    return False
                    
        return True
    except Exception as e:
        llm_utils_logger.error("Error validating LLM response", error=str(e))
        return False

def extract_trading_signals_from_response(response: str) -> List[Dict[str, Any]]:
    """
    Extract trading signals from an LLM response.
    
    Args:
        response: LLM response that may contain trading signals
        
    Returns:
        List[Dict[str, Any]]: List of extracted trading signals
    """
    try:
        signals = []
        
        # Look for common trading signal patterns
        buy_pattern = r'(?:BUY|BUY SIGNAL|GO LONG).*?(?:\b[A-Z]{2,5}\b)'
        sell_pattern = r'(?:SELL|SELL SIGNAL|GO SHORT).*?(?:\b[A-Z]{2,5}\b)'
        hold_pattern = r'(?:HOLD|NEUTRAL|WAIT).*?(?:\b[A-Z]{2,5}\b)'
        
        # Extract buy signals
        buy_matches = re.findall(buy_pattern, response, re.IGNORECASE)
        for match in buy_matches:
            signals.append({
                "signal": "BUY",
                "asset": extract_asset_from_text(match),
                "confidence": extract_confidence_from_text(response)
            })
            
        # Extract sell signals
        sell_matches = re.findall(sell_pattern, response, re.IGNORECASE)
        for match in sell_matches:
            signals.append({
                "signal": "SELL",
                "asset": extract_asset_from_text(match),
                "confidence": extract_confidence_from_text(response)
            })
            
        # Extract hold signals
        hold_matches = re.findall(hold_pattern, response, re.IGNORECASE)
        for match in hold_matches:
            signals.append({
                "signal": "HOLD",
                "asset": extract_asset_from_text(match),
                "confidence": extract_confidence_from_text(response)
            })
            
        return signals
    except Exception as e:
        llm_utils_logger.error("Error extracting trading signals", error=str(e))
        return []

def extract_asset_from_text(text: str) -> str:
    """
    Extract cryptocurrency asset symbol from text.
    
    Args:
        text: Text to extract asset from
        
    Returns:
        str: Extracted asset symbol
    """
    try:
        # Common crypto asset patterns
        crypto_patterns = [
            r'\b(BTC|Bitcoin)\b',
            r'\b(ETH|Ethereum)\b',
            r'\b(XRP|Ripple)\b',
            r'\b(ADA|Cardano)\b',
            r'\b(SOL|Solana)\b',
            r'\b(DOT|Polkadot)\b',
            r'\b(DOGE|Dogecoin)\b',
            r'\b(UNI|Uniswap)\b',
            r'\b(LINK|Chainlink)\b'
        ]
        
        for pattern in crypto_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).upper()
                
        return "UNKNOWN"
    except Exception as e:
        llm_utils_logger.error("Error extracting asset", error=str(e))
        return "UNKNOWN"

def extract_confidence_from_text(text: str) -> float:
    """
    Extract confidence level from text (0.0 to 1.0).
    
    Args:
        text: Text to extract confidence from
        
    Returns:
        float: Confidence level between 0.0 and 1.0
    """
    try:
        # Look for confidence indicators
        high_confidence_patterns = [
            r'\b(high|strong|certain|definite)\b',
            r'\b(90|95|100)%\b'
        ]
        
        medium_confidence_patterns = [
            r'\b(medium|moderate|likely)\b',
            r'\b(70|75|80|85)%\b'
        ]
        
        low_confidence_patterns = [
            r'\b(low|weak|uncertain|possible)\b',
            r'\b(50|55|60|65)%\b'
        ]
        
        text_lower = text.lower()
        
        # Check for high confidence indicators
        for pattern in high_confidence_patterns:
            if re.search(pattern, text_lower):
                return 0.9
                
        # Check for medium confidence indicators
        for pattern in medium_confidence_patterns:
            if re.search(pattern, text_lower):
                return 0.7
                
        # Check for low confidence indicators
        for pattern in low_confidence_patterns:
            if re.search(pattern, text_lower):
                return 0.5
                
        # Default confidence
        return 0.6
    except Exception as e:
        llm_utils_logger.error("Error extracting confidence", error=str(e))
        return 0.5

def ensure_complete_response(response: str) -> str:
    """
    Ensure the response is complete and coherent, not cut off mid-sentence.
    
    Args:
        response: LLM response that may be incomplete
        
    Returns:
        str: Complete and coherent response
    """
    try:
        # If response is empty, return as is
        if not response or not response.strip():
            return response
            
        # Trim whitespace
        response = response.strip()
        
        # Check if the response ends with common sentence-ending punctuation
        if response.endswith(('.', '!', '?', '"', "'")):
            # Response appears to be complete
            return response
            
        # If the response ends with a comma, semicolon, colon, or dash, 
        # it's likely incomplete
        if response.endswith((',', ';', ':', '-', '–')):
            # Try to find the last complete sentence
            last_sentence_end = max(
                response.rfind('.'),
                response.rfind('!'),
                response.rfind('?')
            )
            
            # If we found a sentence ending, truncate to that point
            if last_sentence_end > 0:
                return response[:last_sentence_end + 1].strip()
                
        # If we can't find a good truncation point, it's better to return
        # the incomplete response than to truncate meaninglessly
        return response
        
    except Exception as e:
        llm_utils_logger.warning("Error ensuring complete response", error=str(e))
        # Return original response if we can't process it
        return response
