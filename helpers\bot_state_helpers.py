"""
Bot State Helper Functions

This module provides helper functions for managing bot state information
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from models.bot_state import BotState
from utils.logger import Logger

bot_state_logger = Logger("bot_state_helpers")

async def get_or_create_bot_state(session: AsyncSession, project_id: int) -> BotState:
    """
    Get existing bot state for a project or create a new one if it doesn't exist.
    
    Args:
        session: Database session
        project_id: Project ID to get or create bot state for
        
    Returns:
        BotState: Bot state object
    """
    try:
        # Try to get existing bot state
        stmt = select(BotState).where(BotState.project_id == project_id)
        result = await session.execute(stmt)
        bot_state = result.scalars().first()
        
        # If bot state doesn't exist, create a new one
        if not bot_state:
            bot_state = BotState(
                project_id=project_id,
                last_post_time=None,
                last_reply_time=None
            )
            session.add(bot_state)
            await session.flush()  # Flush to get the ID without committing
            bot_state_logger.info("Created new bot state for project", project_id=project_id)
        else:
            bot_state_logger.info("Found existing bot state for project", project_id=project_id)
            
        return bot_state
    except Exception as e:
        bot_state_logger.error("Error getting or creating bot state for project", project_id=project_id, error=str(e))
        raise

async def update_last_post_time(session: AsyncSession, project_id: int, timestamp: int) -> None:
    """
    Update the last post time for a project.
    
    Args:
        session: Database session
        project_id: Project ID to update
        timestamp: Unix timestamp of the last post
    """
    try:
        bot_state = await get_or_create_bot_state(session, project_id)
        bot_state.last_post_time = timestamp
        session.add(bot_state)
        # Note: This function doesn't commit the transaction, caller is responsible for committing
        bot_state_logger.info("Updated last post time for project", project_id=project_id, timestamp=timestamp)
    except Exception as e:
        bot_state_logger.error("Error updating last post time for project", project_id=project_id, error=str(e))
        raise

async def update_last_reply_time(session: AsyncSession, project_id: int, timestamp: int) -> None:
    """
    Update the last reply time for a project.
    
    Args:
        session: Database session
        project_id: Project ID to update
        timestamp: Unix timestamp of the last reply
    """
    try:
        bot_state = await get_or_create_bot_state(session, project_id)
        bot_state.last_reply_time = timestamp
        session.add(bot_state)
        # Note: This function doesn't commit the transaction, caller is responsible for committing
        bot_state_logger.info("Updated last reply time for project", project_id=project_id, timestamp=timestamp)
    except Exception as e:
        bot_state_logger.error("Error updating last reply time for project", project_id=project_id, error=str(e))
        raise

async def get_last_post_time(session: AsyncSession, project_id: int) -> int:
    """
    Get the last post time for a project.
    
    Args:
        session: Database session
        project_id: Project ID to get last post time for
        
    Returns:
        int: Unix timestamp of the last post, or None if not set
    """
    try:
        bot_state = await get_or_create_bot_state(session, project_id)
        return bot_state.last_post_time
    except Exception as e:
        bot_state_logger.error("Error getting last post time for project", project_id=project_id, error=str(e))
        raise

async def get_last_reply_time(session: AsyncSession, project_id: int) -> int:
    """
    Get the last reply time for a project.
    
    Args:
        session: Database session
        project_id: Project ID to get last reply time for
        
    Returns:
        int: Unix timestamp of the last reply, or None if not set
    """
    try:
        bot_state = await get_or_create_bot_state(session, project_id)
        return bot_state.last_reply_time
    except Exception as e:
        bot_state_logger.error("Error getting last reply time for project", project_id=project_id, error=str(e))
        raise