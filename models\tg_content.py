from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Integer, String, Text, ForeignKey, CheckConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship, validates
from models.base import Base

class TgContent(Base):
    __tablename__ = 'tg_content'

    # Add a check constraint to prevent empty content at database level
    __table_args__ = (
        CheckConstraint("LENGTH(TRIM(content)) > 0", name="check_content_not_empty"),
    )

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    entities_id: Mapped[int] = mapped_column(Integer, ForeignKey('tg_entities.id'), nullable=False)
    thread_id: Mapped[int] = mapped_column(BigInteger, nullable=True)  # Made nullable
    user_id: Mapped[str] = mapped_column(String(255), nullable=False)
    message_id: Mapped[int] = mapped_column(BigInteger, nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    message_date: Mapped[int] = mapped_column(BigInteger, nullable=False)  # Epoch Time
    reply_to: Mapped[int] = mapped_column(BigInteger, nullable=True)
    created_at: Mapped[int] = mapped_column(BigInteger, nullable=False)

    # Relationships
    entity: Mapped["TgEntity"] = relationship("TgEntity", back_populates="tg_contents")

    @validates('content')
    def validate_content(self, key, content):
        """Validate that content is not empty or whitespace-only."""
        if not content or not content.strip():
            raise ValueError("Content cannot be empty or whitespace-only")
        return content