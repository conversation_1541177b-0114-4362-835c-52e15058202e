"""
Message formatter helper functions for the Telegram Scraper application.
This module contains utility functions for formatting messages in the messages table.
"""

import re
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from utils.logger import Logger
from models.message import Message
from models.tg_entity import TgEntity

message_formatter_logger = Logger("message_formatter")

async def format_message_link(session: AsyncSession, message: Message) -> str:
    """
    Format message link to the specified format:
    'Sent Telegram message - https://t.me/tgtelegramgroup1/17' or 
    'Reply Telegram message - https://t.me/tgtelegramgroup1/17'
    
    Args:
        session (AsyncSession): Database session
        message (Message): Message object to format
        
    Returns:
        str: Formatted message link
    """
    try:
        # Determine message type (Sent or Reply)
        # If message type is "reply_telegram_post" or if reply_to exists, it's a Reply
        # Otherwise, it's a Sent message
        # Per user requirement: All messages are considered "Sent Telegram message" in UI
        # even if they are stored as "reply_telegram_post" in the database
        is_reply = (message.type == "reply_telegram_post") or (message.reply_to is not None)
        # Always show "Sent" regardless of actual message type
        message_type = "Sent"
        
        # Get the Telegram entity information
        if message.entities_id:
            stmt = select(TgEntity).where(TgEntity.id == message.entities_id)
            result = await session.execute(stmt)
            entity = result.scalars().first()
            
            if entity:
                # For Telegram URLs, we need to handle different cases:
                # 1. Public channels/groups: Use the username (name) in the URL
                # 2. Private channels/groups: Use the tg_id in the URL with c/ prefix for channels
                # 3. Groups without usernames: Can't be directly linked, need to use message ID differently
                
                # Check if this is a private entity (is_private = 1)
                if entity.is_private == 1:
                    # For private entities, we use the tg_id
                    # For channels, the format is https://t.me/c/tg_id/message_id
                    # For groups, it's more complex and may not be directly linkable
                    tg_url = f"https://t.me/c/{entity.tg_id}/{message.message_id}"
                else:
                    # For public entities, try to use the name but make it URL-safe
                    if entity.name:
                        # For public channels/groups, the name should be the username
                        # Remove @ symbol if present (some names might include it)
                        clean_name = entity.name.lstrip('@')
                        
                        # If the name contains non-English characters, we might need to use tg_id instead
                        # But for now, let's try to use the name as is for public channels
                        # Telegram URLs can handle Unicode characters in some cases
                        tg_url = f"https://t.me/{clean_name}/{message.message_id}"
                    else:
                        # Fallback to using tg_id if name is not available
                        tg_url = f"https://t.me/c/{entity.tg_id}/{message.message_id}"
                
                # Format the message link
                formatted_link = f"{message_type} Telegram message - {tg_url}"
                return formatted_link
        
        # Fallback if entity information is not available
        formatted_link = f"{message_type} Telegram message - ID: {message.message_id}"
        return formatted_link
        
    except Exception as e:
        message_formatter_logger.error(
            "Error formatting message link", message_id=message.id, error=str(e)
        )
        # Return a default formatted link if formatting fails
        # Always show "Sent" regardless of actual message type
        message_type = "Sent"
        return f"{message_type} Telegram message - ID: {message.message_id}"

async def convert_messages_to_formatted_links(session: AsyncSession):
    """
    Convert message links in the messages table to the specified format.
    Only updates the message field, not the content field.
    
    Args:
        session (AsyncSession): Database session
    """
    try:
        # Get all messages that need to be formatted
        stmt = select(Message)
        result = await session.execute(stmt)
        messages = result.scalars().all()
        
        updated_count = 0
        
        for message in messages:
            # Only format messages that are telegram posts (both regular and reply types)
            if message.type in ["telegram_post", "reply_telegram_post"]:
                # Format the message link
                formatted_link = await format_message_link(session, message)
                
                # Update only the message field if it's different
                if formatted_link != message.message:
                    message.message = formatted_link
                    updated_count += 1
        
        if updated_count > 0:
            await session.commit()
            message_formatter_logger.info(
                "Converted message links to formatted links", updated_count=updated_count
            )
        
        return updated_count
        
    except Exception as e:
        message_formatter_logger.error(
            "Error converting message links to formatted links", error=str(e)
        )
        await session.rollback()
        raise
