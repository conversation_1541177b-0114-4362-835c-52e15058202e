"""
In-memory state manager for the Telegram Scraper application.
This module contains utility functions for managing scraper state in memory.
"""

import datetime
import pytz
from typing import Dict, Any
from utils.logger import Logger

# Only log initialization to avoid verbose logging during normal operations
in_memory_state_logger = Logger("in_memory_state_manager")

class InMemoryStateManager:
    """In-memory state manager for tracking scraping progress (clears on restart)"""
    
    def __init__(self):
        self.state = {}
        # Log only during initialization to avoid verbose logging during normal operations
        in_memory_state_logger.info("Initialized in-memory state manager")
        
    def get_last_processed_id(self, channel_key: str) -> int:
        """Get the last processed message ID for a channel"""
        return self.state.get('channel_states', {}).get(channel_key, 0)
        
    def update_last_processed_id(self, channel_key: str, message_id: int):
        """Update the last processed message ID for a channel"""
        current_id = self.get_last_processed_id(channel_key)
        if message_id > current_id:
            if 'channel_states' not in self.state:
                self.state['channel_states'] = {}
            self.state['channel_states'][channel_key] = message_id
            
    def get_start_time(self, channel_key: str) -> int:
        """Get the start time for a channel (for historical scraping)"""
        return self.state.get('channel_start_times', {}).get(channel_key, 0)
        
    def set_historical_start_time(self, channel_key: str, days_back: int = 90):
        """
        Set the start time for historical scraping.
        
        Args:
            channel_key (str): Channel identifier
            days_back (int): Number of days to go back (default: 90 days = 3 months)
        """
        # Calculate the timestamp for the specified number of days ago
        # Use UTC timezone for consistency
        utc_now = datetime.datetime.now(pytz.UTC)
        historical_time = int((utc_now - datetime.timedelta(days=days_back)).timestamp())
        if 'channel_start_times' not in self.state:
            self.state['channel_start_times'] = {}
        self.state['channel_start_times'][channel_key] = historical_time
        
    def get_last_scrape_time(self, channel_key: str) -> int:
        """Get the last scrape time for a channel"""
        return self.state.get('channel_last_scrape', {}).get(channel_key, 0)
        
    def update_last_scrape_time(self, channel_key: str, scrape_time: int):
        """Update the last scrape time for a channel"""
        if 'channel_last_scrape' not in self.state:
            self.state['channel_last_scrape'] = {}
        self.state['channel_last_scrape'][channel_key] = scrape_time