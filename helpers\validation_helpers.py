"""
Validation helper functions for the Telegram Scraper application.
This module contains utility functions for validating content quality and detecting fake information.
"""

import re
from typing import List, Dict, Any
from utils.logger import Logger

validation_helper_logger = Logger("validation_helper")

def detect_fake_content(content: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Detect if content is likely to be fake or low quality.
    
    Args:
        content (str): Content to analyze
        
    Returns:
        Dict[str, Any]: Analysis result with is_fake flag and reasons
    """
    try:
        if not content or not content.strip():
            return {
                "is_fake": True,
                "reasons": ["empty_content"],
                "confidence": 1.0
            }
        
        content = content.strip()
        reasons = []
        confidence = 0.0
        
        # Check for common fake content patterns
        fake_patterns = [
            (r'\b(breaking news|urgent|alert|warning)\b.*?\b(click|link|here|now)\b', 0.8, "clickbait_pattern"),
            (r'\b(guaranteed|100%|risk free|no risk)\b.*?\b(profit|return|gain)\b', 0.7, "unrealistic_guarantees"),
            (r'\b(secret|insider|exclusive)\b.*?\b(information|tip|deal)\b', 0.6, "sensational_claims"),
            (r'\b(urgent|limited time|act now|don\'t miss)\b', 0.5, "urgency_tactics"),
            (r'\b(amazing|incredible|unbelievable)\b.*?\b(opportunity|deal|chance)\b', 0.6, "exaggerated_claims"),
            (r'\b(winner|winning|won)\b.*?\b(lottery|prize|jackpot)\b', 0.9, "lottery_scam"),
            (r'\b(free|gratis)\b.*?\b(money|cash|gift)\b', 0.7, "free_money_scam"),
            (r'\b(investment|trading)\b.*?\b(guaranteed|sure|certain)\b.*?\b(profit|return)\b', 0.8, "investment_scam"),
        ]
        
        content_lower = content.lower()
        for pattern, conf, reason in fake_patterns:
            if re.search(pattern, content_lower):
                reasons.append(reason)
                confidence = max(confidence, conf)
        
        # Check for excessive capitalization (shouting)
        if len(content) > 20:
            uppercase_count = sum(1 for c in content if c.isupper())
            if uppercase_count / len(content) > 0.7:  # Increased threshold to 70%
                reasons.append("excessive_capitalization")
                confidence = max(confidence, 0.6)
        
        # Check for excessive exclamation marks or question marks
        exclamation_count = content.count('!')
        question_count = content.count('?')
        if exclamation_count > 10 or question_count > 10:  # Increased thresholds
            reasons.append("excessive_punctuation")
            confidence = max(confidence, 0.5)
        
        # Check for suspicious URL patterns
        url_patterns = [
            (r'https?://[^\s]*?(?:bit\.ly|tinyurl|t\.co)', 0.7, "suspicious_short_url"),
            (r'https?://[^\s]*?(?:earn|money|cash|free)', 0.8, "monetized_url"),
        ]
        
        for pattern, conf, reason in url_patterns:
            if re.search(pattern, content_lower):
                reasons.append(reason)
                confidence = max(confidence, conf)
        
        # Check for nonsensical or repetitive content
        if len(content) > 50:
            # Simple repetition check
            words = content.split()
            if len(words) > 10:
                unique_words = set(words)
                repetition_ratio = 1 - (len(unique_words) / len(words))
                if repetition_ratio > 0.7:  # Increased threshold to 70%
                    reasons.append("repetitive_content")
                    confidence = max(confidence, 0.6)
        
        # Check for extremely short content with high confidence scam indicators
        if len(content) < 100:
            short_content_patterns = [
                (r'\b(prize|winner|congratulations)\b', 0.9, "lottery_notification"),
                (r'\b(verify|confirm)\b.*?\b(account|identity)\b', 0.8, "phishing_attempt"),
                (r'\b(urgent|immediate)\b.*?\b(action|required)\b', 0.7, "urgent_action_request"),
            ]
            
            for pattern, conf, reason in short_content_patterns:
                if re.search(pattern, content_lower):
                    reasons.append(reason)
                    confidence = max(confidence, conf)
        
        # Additional checks for legitimate trading content
        legitimate_patterns = [
            r'\b(BUY|SELL|HOLD)\b.*?\b[A-Z]{2,5}\b',  # Trading signals
            r'\b(analysis|market|trend|support|resistance|indicators?)\b',  # Technical terms
            r'\b(RSI|MACD|moving average|technical analysis)\b',  # Technical indicators
        ]
        
        # If content contains legitimate trading terms, reduce fake confidence
        legitimate_matches = sum(1 for pattern in legitimate_patterns if re.search(pattern, content, re.IGNORECASE))
        if legitimate_matches >= 2:
            confidence = max(0.0, confidence - 0.3)  # Reduce confidence if legitimate terms are present
        
        # Get configurable threshold from config
        threshold = 0.5  # Default threshold
        if config and config.get("application", {}).get("validation_thresholds", {}).get("fake_content_confidence"):
            threshold = config["application"]["validation_thresholds"]["fake_content_confidence"]
        
        # Check if validation should be bypassed
        bypass_validation = config and config.get("application", {}).get("bypass_content_validation", False)
        debug_mode = config and config.get("application", {}).get("debug_mode", False)
        
        is_fake = len(reasons) > 0 and confidence > threshold
        
        # Override fake detection if bypass is enabled or debug mode is on
        if bypass_validation or debug_mode:
            is_fake = False
        
        return {
            "is_fake": is_fake,
            "reasons": reasons,
            "confidence": confidence,
            "threshold_used": threshold,
            "bypass_enabled": bypass_validation or debug_mode
        }
        
    except Exception as e:
        validation_helper_logger.error("Error detecting fake content", error=str(e))
        return {
            "is_fake": False,
            "reasons": [],
            "confidence": 0.0
        }

def validate_trading_signal_content(content: str, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Validate trading signal content for quality and authenticity.
    
    Args:
        content (str): Trading signal content to validate
        
    Returns:
        Dict[str, Any]: Validation result
    """
    try:
        if not content or not content.strip():
            return {
                "is_valid": False,
                "reasons": ["empty_content"],
                "confidence": 1.0
            }
        
        content = content.strip()
        reasons = []
        confidence = 0.0
        
        # Check for specific trading signal validation patterns
        trading_validation_patterns = [
            (r'\b(BUY|SELL|HOLD)\b.*?\b[A-Z]{2,5}\b', 0.9, "valid_signal_format"),
            (r'\b(target|stop loss|take profit|entry)\b.*?\b(price|level)\b', 0.8, "detailed_analysis"),
            (r'\b(analysis|market|trend|support|resistance)\b', 0.7, "technical_terms"),
            (r'\b(confirmation|indicators?|RSI|MACD|moving average)\b', 0.7, "technical_indicators"),
        ]
        
        content_lower = content.lower()
        valid_indicators = 0
        for pattern, conf, reason in trading_validation_patterns:
            if re.search(pattern, content_lower):
                valid_indicators += 1
        
        # Check for unrealistic price predictions
        price_prediction_patterns = [
            (r'\b(target|price)\b.*?\b(1000x|100x|100x)\b', 0.9, "unrealistic_multiplier"),
            (r'\b(moon|to the moon|lambo)\b', 0.7, "hype_language"),
            (r'\b(guaranteed|sure|certain)\b.*?\b(profit|return|gain)\b.*?\b(100%|[0-9]{2,}%)\b', 0.8, "guaranteed_high_return"),
        ]
        
        for pattern, conf, reason in price_prediction_patterns:
            if re.search(pattern, content_lower):
                reasons.append(reason)
                confidence = max(confidence, conf)
        
        # Check for proper structure (should have asset, action, and some reasoning)
        required_elements = [
            r'\b[A-Z]{2,5}\b',  # Asset symbol
            r'\b(BUY|SELL|HOLD|LONG|SHORT)\b',  # Action
        ]
        
        structure_score = sum(1 for pattern in required_elements if re.search(pattern, content, re.IGNORECASE))
        
        # Additional elements that improve validity
        additional_elements = [
            r'\b(because|due to|reason|analysis|indicators?|target|stop|support|resistance)\b',  # Reasoning
            r'\b(\d+(?:\.\d+)?)\b',  # Numbers/prices
        ]
        
        additional_score = sum(1 for pattern in additional_elements if re.search(pattern, content_lower))
        
        # If content has trading signal format but lacks technical details, it might be fake
        has_signal_format = any(re.search(r'\b(BUY|SELL|HOLD)\b.*?\b[A-Z]{2,5}\b', content, re.IGNORECASE) for _ in range(1))
        has_technical_details = valid_indicators >= 1 or additional_score >= 1
        
        if has_signal_format and not has_technical_details:
            reasons.append("signal_without_analysis")
            confidence = max(confidence, 0.6)
        
        # Get configurable threshold from config
        threshold = 0.7  # Default threshold
        if config and config.get("application", {}).get("validation_thresholds", {}).get("trading_signal_confidence"):
            threshold = config["application"]["validation_thresholds"]["trading_signal_confidence"]
        
        # Check if validation should be bypassed
        bypass_validation = config and config.get("application", {}).get("bypass_content_validation", False)
        debug_mode = config and config.get("application", {}).get("debug_mode", False)
        
        # More lenient validation - if it has the basic structure, consider it valid unless there are red flags
        is_valid = (structure_score >= 2) and (len(reasons) == 0 or confidence < threshold)
        
        # Override validation if bypass is enabled or debug mode is on
        if bypass_validation or debug_mode:
            is_valid = True
        
        return {
            "is_valid": is_valid,
            "reasons": reasons,
            "confidence": confidence,
            "structure_score": structure_score,
            "additional_score": additional_score,
            "threshold_used": threshold,
            "bypass_enabled": bypass_validation or debug_mode
        }
        
    except Exception as e:
        validation_helper_logger.error("Error validating trading signal content", error=str(e))
        return {
            "is_valid": False,
            "reasons": ["validation_error"],
            "confidence": 0.0,
            "structure_score": 0,
            "additional_score": 0
        }

def is_content_sufficiently_detailed(content: str, min_word_count: int = 10, config: Dict[str, Any] = None) -> bool:
    """
    Check if content is sufficiently detailed.
    
    Args:
        content (str): Content to check
        min_word_count (int): Minimum number of words required
        
    Returns:
        bool: True if content is sufficiently detailed, False otherwise
    """
    try:
        if not content or not content.strip():
            return False
        
        # Get configurable min word count from config
        if config and config.get("application", {}).get("validation_thresholds", {}).get("min_word_count"):
            min_word_count = config["application"]["validation_thresholds"]["min_word_count"]
        
        # Check if validation should be bypassed
        bypass_validation = config and config.get("application", {}).get("bypass_content_validation", False)
        debug_mode = config and config.get("application", {}).get("debug_mode", False)
        
        if bypass_validation or debug_mode:
            return True
        
        word_count = len(content.strip().split())
        return word_count >= min_word_count
        
    except Exception as e:
        validation_helper_logger.error("Error checking content detail level", error=str(e))
        return False