# LangGraph Components

This directory contains the core implementation of LangGraph workflows used in the tg-scraper project. LangGraph is a library for building stateful, multi-step AI workflows, particularly useful for creating complex agent behaviors.

## Overview

The LangGraph implementation in this project provides a framework for creating stateful workflows that can process messages, analyze content, and generate responses using Large Language Models (LLMs). It's particularly used for processing Telegram messages and generating crypto analysis.

## Components

### Core Module (`core.py`)

The core module contains the foundational classes for building LangGraph workflows:

- **GraphState**: A dataclass that defines the state structure for workflows, including messages, context, response, and other metadata.
- **LanggraphWorkflow**: Base class for all LangGraph workflows with methods for adding nodes, edges, and executing workflows.
- **Specialized Workflows**:
  - **MessageProcessingWorkflow**: For processing general messages through preprocessing, analysis, and postprocessing steps.
  - **TelegramMessageProcessingWorkflow**: Specialized version for Telegram messages with thread support.
  - **CryptoAnalysisWorkflow**: For extracting crypto signals, analyzing market conditions, and generating unique content.
  - **ContentGenerationWorkflow**: For generating unique content with topic definition, style selection, and review processes.

### Factory Module (`factory.py`)

The factory module provides convenient functions for creating different types of workflows:

- **create_workflow()**: Creates workflows based on type (message_processing, telegram, crypto, content, etc.)
- **create_custom_workflow()**: Creates custom workflows with specified nodes and edges
- **Specialized factory functions** for creating specific workflow types

## Usage

### Creating a Basic Workflow

```python
from helpers.langgraph.factory import create_workflow

# Create a default workflow
workflow = create_workflow("default", "my_workflow")

# Add nodes and edges
workflow.add_node("step1", my_function_1)
workflow.add_node("step2", my_function_2)
workflow.add_edge("step1", "step2")
workflow.set_entry_point("step1")
workflow.set_finish_point("step2")

# Compile and run
workflow.compile()
result = await workflow.invoke(initial_state)
```

### Creating Specialized Workflows

```python
from helpers.langgraph.factory import create_crypto_analysis_workflow

# Create a crypto analysis workflow
crypto_workflow = create_crypto_analysis_workflow("my_crypto_analysis")

# Run with initial state
initial_state = {
    "messages": [...],
    "context": {...}
}
result = await crypto_workflow.invoke(initial_state)
```

## Workflow Types

1. **Default Workflow**: Basic workflow with no predefined nodes
2. **Message Processing**: Three-step workflow (preprocess, analyze, postprocess)
3. **Telegram Processing**: Message processing with Telegram-specific features
4. **Crypto Analysis**: Four-step workflow for crypto signal extraction and content generation
5. **Content Generation**: Four-step workflow for general content creation

## State Management

Each workflow maintains state through the `GraphState` dataclass, which includes:
- `messages`: Sequence of messages being processed
- `context`: Dictionary of contextual information
- `response`: Generated response
- `next_action`: Next action to take
- `is_complete`: Whether the workflow is complete
- `metadata`: Additional metadata
- Specialized fields for crypto and content generation workflows

## Integration with Other Components

The LangGraph components integrate with:
- LLM helpers for AI processing
- Database services for data persistence
- Logging for monitoring and debugging

## Debugging Common Errors

### 1. Workflow Compilation Errors
- **Symptom**: `ValueError` when compiling workflow
- **Cause**: Missing nodes, incorrect node names in edges, or circular dependencies
- **Solution**: 
  - Verify all node names match exactly in `add_node()` and `add_edge()` calls
  - Check that all nodes referenced in edges actually exist
  - Ensure entry and finish points are correctly set

### 2. Node Execution Errors
- **Symptom**: Exception during workflow execution
- **Cause**: Errors in node functions or invalid state transitions
- **Solution**:
  - Check node function signatures - they must accept a `GraphState` parameter
  - Ensure node functions return dictionaries with valid state updates
  - Look at the logs for specific error messages and stack traces

### 3. State Management Issues
- **Symptom**: Unexpected workflow behavior or missing data
- **Cause**: Incorrect state updates in node functions
- **Solution**:
  - Verify that node functions return dictionaries with proper state keys
  - Check that state modifications don't mutate the original state directly
  - Use logging to trace state changes throughout the workflow

### 4. Async/Await Issues
- **Symptom**: `RuntimeError` or unexpected behavior with async operations
- **Cause**: Missing `await` keywords or incorrect async handling
- **Solution**:
  - Ensure all async functions are properly awaited
  - Use `await workflow.invoke()` rather than `workflow.invoke()`
  - Check that node functions are async if they perform async operations

## Logging and Monitoring

All LangGraph operations are logged using the project's logging system:
- INFO level: Workflow creation, compilation, and successful execution
- DEBUG level: Detailed node operations and state transitions
- ERROR level: Workflow compilation errors and execution failures

To enable debug logging, set the log level to DEBUG in the configuration.

## Recent Enhancements

### Enhanced Reply Handling
- Improved reply-to message processing with accurate original message retrieval
- Enhanced context handling for reply messages using both message_id and entities_id matching
- Better handling of message thread IDs for forum topics

### Content Quality Improvements
- Enhanced validation to detect and filter placeholder content (e.g., "XXX" values)
- Improved response validation to prevent low-quality content from being processed
- Added checks for meaningful content length and structure

### Performance Optimizations
- Optimized workflow execution with better state management
- Reduced unnecessary processing of low-quality content
- Improved error handling and logging for debugging purposes

## Project Integration

This component is part of the larger Telegram Scraper project. For more information about the overall architecture and how this component fits in, see the [main project README](../../README.md).