"""
Telegram helper functions for the Telegram Scraper application.
This module contains utility functions for Telegram operations.
"""

import re
import os
import time
import asyncio
import datetime
import traceback
from typing import Dict, Any, List, Optional
from utils.logger import Logger
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat, User
from telethon.tl.functions.messages import GetHistoryRequest
import sqlite3

tg_helper_logger = Logger("telegram_helper")

def validate_phone_number(phone_number: str) -> bool:
    """
    Validate phone number format for Telegram.
    
    Args:
        phone_number (str): Phone number to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    # Telegram phone numbers should start with + and contain only digits
    pattern = r'^\+[1-9]\d{1,14}$'  # E.164 format
    return bool(re.match(pattern, phone_number))

def validate_api_id(api_id: int) -> bool:
    """
    Validate Telegram API ID.
    
    Args:
        api_id (int): API ID to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    # API ID should be a positive integer
    return isinstance(api_id, int) and api_id > 0

def validate_api_hash(api_hash: str) -> bool:
    """
    Validate Telegram API hash.
    
    Args:
        api_hash (str): API hash to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    # API hash should be a non-empty string
    return isinstance(api_hash, str) and len(api_hash) > 0

def format_channel_info(channel_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format channel information for consistent output.
    
    Args:
        channel_data (Dict[str, Any]): Raw channel data
        
    Returns:
        Dict[str, Any]: Formatted channel data
    """
    formatted = {
        "id": channel_data.get("id"),
        "title": channel_data.get("title", "Unknown"),
        "username": channel_data.get("username"),
        "participants_count": channel_data.get("participants_count", 0),
        "type": "channel" if channel_data.get("megagroup", False) else "group",
        "is_private": channel_data.get("is_private", False)
    }
    return formatted

def extract_message_entities(message: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract entities from a Telegram message.
    
    Args:
        message (Dict[str, Any]): Telegram message data
        
    Returns:
        Dict[str, Any]: Extracted message entities
    """
    entities = {
        "id": message.get("id"),
        "date": message.get("date"),
        "message": message.get("message", ""),
        "sender_id": message.get("sender_id"),
        "reply_to_msg_id": message.get("reply_to_msg_id"),
        "media": message.get("media"),
        "views": message.get("views", 0),
        "forwards": message.get("forwards", 0)
    }
    return entities

def format_user_info(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format user information for consistent output.
    
    Args:
        user_data (Dict[str, Any]): Raw user data
        
    Returns:
        Dict[str, Any]: Formatted user data
    """
    formatted = {
        "id": user_data.get("id"),
        "username": user_data.get("username"),
        "first_name": user_data.get("first_name", ""),
        "last_name": user_data.get("last_name", ""),
        "phone": user_data.get("phone"),
        "is_bot": user_data.get("bot", False)
    }
    return formatted

def sanitize_message_content(message: str) -> str:
    """
    Sanitize message content for safe storage/display.
    
    Args:
        message (str): Raw message content
        
    Returns:
        str: Sanitized message content
    """
    if not message:
        return ""
    
    # Remove or escape potentially harmful characters
    # Enhanced implementation with more robust sanitization
    import html
    import re
    
    # Remove null bytes and other control characters
    sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', message)
    
    # Escape HTML characters to prevent XSS
    sanitized = html.escape(sanitized)
    
    # Limit message length to prevent excessive data storage
    max_length = 4096  # Telegram's max message length
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized

def parse_tg_link(link: str) -> Dict[str, str]:
    """
    Parse a Telegram link to extract entity information.
    
    Args:
        link (str): Telegram link
        
    Returns:
        Dict[str, str]: Parsed link information
    """
    # Regular expressions for different Telegram link formats
    patterns = {
        "public_channel": r"^https?://t\.me/([a-zA-Z0-9_]{5,32})$",
        "private_channel": r"^https?://t\.me/joinchat/([a-zA-Z0-9_-]+)$",
        "user": r"^https?://t\.me/(@[a-zA-Z0-9_]{5,32})$"
    }
    
    for link_type, pattern in patterns.items():
        match = re.match(pattern, link)
        if match:
            return {
                "type": link_type,
                "identifier": match.group(1)
            }
    
    return {
        "type": "unknown",
        "identifier": None
    }

def check_session_file_exists(phone_number: str) -> bool:
    """
    Check if a session file exists for the given phone number.
    
    Args:
        phone_number (str): Phone number associated with the session
        
    Returns:
        bool: True if session file exists, False otherwise
    """
    session_path = f'sessions/session_{phone_number}'
    session_file_path = f'{session_path}.session'  # Telethon adds .session extension
    
    return os.path.exists(session_file_path)

async def create_telegram_client(phone_number: str, api_id: int, api_hash: str, bot_token: str) -> TelegramClient:
    """
    Create a Telegram client for the given user with database lock prevention.

    Args:
        phone_number (str): Phone number of the user
        api_id (int): Telegram API ID
        api_hash (str): Telegram API hash
        bot_token (str): Telegram bot token

    Returns:
        TelegramClient: Initialized Telegram client
    """
    # Ensure sessions directory exists
    os.makedirs('sessions', exist_ok=True)

    session_path = f'sessions/session_{phone_number}'

    # Check for existing database locks and handle them
    max_retries = 3
    for attempt in range(max_retries):
        try:
            # Try to create the client
            client = TelegramClient(session_path, api_id, api_hash)

            # Set bot token if provided
            if bot_token:
                client.bot_token = bot_token

            return client

        except Exception as e:
            if "database is locked" in str(e).lower() and attempt < max_retries - 1:
                tg_helper_logger.warning(f"Database lock detected for {phone_number}, attempt {attempt + 1}/{max_retries}")
                # Wait with exponential backoff
                wait_time = (attempt + 1) * 2
                await asyncio.sleep(wait_time)

                # Try to handle the database lock
                if not handle_database_lock(phone_number):
                    tg_helper_logger.warning(f"Failed to handle database lock for {phone_number}")
            else:
                raise

async def create_unique_telegram_client(session_id: str, api_id: int, api_hash: str, bot_token: str = None) -> TelegramClient:
    """
    Create a Telegram client with a unique session ID to prevent race conditions.

    Args:
        session_id (str): Unique session identifier
        api_id (int): Telegram API ID
        api_hash (str): Telegram API hash
        bot_token (str): Telegram bot token (optional)

    Returns:
        TelegramClient: Initialized Telegram client with unique session
    """
    # Ensure sessions directory exists
    os.makedirs('sessions', exist_ok=True)

    session_path = f'sessions/session_{session_id}'
    client = TelegramClient(session_path, api_id, api_hash)

    # Set bot token if provided
    if bot_token:
        client.bot_token = bot_token

    return client

def cleanup_unique_session_files(session_id: str) -> bool:
    """
    Clean up unique session files to prevent accumulation.

    Args:
        session_id (str): Unique session identifier to clean up

    Returns:
        bool: True if cleanup was successful, False otherwise
    """
    try:
        session_path = f'sessions/session_{session_id}'
        session_file_path = f'{session_path}.session'
        journal_file_path = f'{session_path}.session-journal'

        # Remove session file if it exists
        if os.path.exists(session_file_path):
            os.remove(session_file_path)
            tg_helper_logger.info("Removed unique session file", session_id=session_id)

        # Remove journal file if it exists
        if os.path.exists(journal_file_path):
            os.remove(journal_file_path)
            tg_helper_logger.info("Removed unique session journal file", session_id=session_id)

        return True

    except Exception as e:
        tg_helper_logger.warning("Could not clean up unique session files", session_id=session_id, error=str(e))
        return False

async def get_channel_messages(client: TelegramClient, channel_id: int, access_hash: str, last_processed_id: int = 0) -> List:
    """
    Get messages from a Telegram channel.
    
    Args:
        client (TelegramClient): Telegram client
        channel_id (int): Channel ID
        access_hash (str): Access hash for the channel
        last_processed_id (int): Last processed message ID (to avoid duplicates)
        
    Returns:
        List: List of messages
    """
    try:
        # Get the channel entity
        if access_hash and access_hash.isdigit():
            from telethon.tl.types import InputPeerChannel
            channel_entity = InputPeerChannel(channel_id, int(access_hash))
        else:
            channel_entity = await client.get_entity(channel_id)
        
        # Get message history
        messages = await client(GetHistoryRequest(
            peer=channel_entity,
            limit=100,  # Get up to 100 messages at a time
            offset_date=None,
            offset_id=0,  # Start from the most recent messages
            max_id=last_processed_id if last_processed_id > 0 else 0,  # Don"t include messages with IDs less than or equal to last_processed_id
            min_id=0,
            add_offset=0,
            hash=0
        ))
        
        # Filter out messages that are older than or equal to the last processed message
        # and messages without content
        filtered_messages = []
        for message in messages.messages:
            if message.id > last_processed_id:
                # Check for content in both message.message and message.text attributes
                message_content = getattr(message, 'message', '') or getattr(message, 'text', '') or ''
                if message_content.strip():
                    filtered_messages.append(message)
                
        # Note: Logging is handled by the calling service to avoid duplicate log entries
            
        return filtered_messages
        
    except Exception as e:
        tg_helper_logger.error("Error getting messages from channel", channel_id=channel_id, error=str(e))
        return []

async def get_historical_channel_messages(client: TelegramClient, channel_id: int, access_hash: str, start_date: datetime.datetime, end_date: datetime.datetime = None) -> List:
    """
    Get historical messages from a Telegram channel within a date range.
    
    Args:
        client (TelegramClient): Telegram client
        channel_id (int): Channel ID
        access_hash (str): Access hash for the channel
        start_date (datetime.datetime): Start date for historical scraping
        end_date (datetime.datetime): End date for historical scraping (default: current date)
        
    Returns:
        List: List of messages within the specified date range
    """
    try:
        # Get the channel entity
        if access_hash and access_hash.isdigit():
            from telethon.tl.types import InputPeerChannel
            channel_entity = InputPeerChannel(channel_id, int(access_hash))
        else:
            channel_entity = await client.get_entity(channel_id)
        
        if end_date is None:
            end_date = datetime.datetime.now()
            
        # Ensure both dates have the same timezone awareness
        # If start_date is naive, make end_date naive too
        if start_date.tzinfo is None:
            if end_date.tzinfo is not None:
                # Convert end_date to naive
                end_date = end_date.replace(tzinfo=None)
        else:
            # If start_date is aware, make end_date aware too
            if end_date.tzinfo is None:
                # This is more complex - we'll make start_date naive for simplicity
                start_date = start_date.replace(tzinfo=None)
                end_date = end_date.replace(tzinfo=None)
            
        # Calculate the number of days to scrape
        days_to_scrape = (end_date - start_date).days
        tg_helper_logger.info("Scraping days of historical data for channel", days_to_scrape=days_to_scrape, channel_id=channel_id)
        
        all_messages = []
        current_date = end_date
        offset_id = 0
        
        # Scrape messages in reverse chronological order (newest first)
        while current_date > start_date:
            # Get message history
            messages = await client(GetHistoryRequest(
                peer=channel_entity,
                limit=100,  # Get up to 100 messages at a time
                offset_date=current_date,
                offset_id=offset_id,
                max_id=0,
                min_id=0,
                add_offset=0,
                hash=0
            ))
            
            if not messages.messages:
                # No more messages, break the loop
                break
                
            # Filter messages within date range
            filtered_messages = []
            oldest_message_date = None
            
            for message in messages.messages:
                # Check if message has a date and content
                if hasattr(message, 'date'):
                    # Check for content in both message.message and message.text attributes
                    message_content = getattr(message, 'message', '') or getattr(message, 'text', '') or ''
                    if message_content.strip():
                        # Ensure message.date has the same timezone awareness as our dates
                        message_date = message.date
                        if message_date.tzinfo is not None and start_date.tzinfo is None:
                            # Convert message_date to naive
                            message_date = message_date.replace(tzinfo=None)
                        elif message_date.tzinfo is None and start_date.tzinfo is not None:
                            # This is more complex - we'll make start_date naive for simplicity
                            start_date = start_date.replace(tzinfo=None)
                            end_date = end_date.replace(tzinfo=None)
                            current_date = current_date.replace(tzinfo=None)

                        # Check if message is within our date range
                        if start_date <= message_date <= current_date:
                            filtered_messages.append(message)
                            # Track the oldest message date for the next iteration
                            if oldest_message_date is None or message_date < oldest_message_date:
                                oldest_message_date = message_date
                        # If message is older than our start date, we can stop
                        elif message_date < start_date:
                            oldest_message_date = message_date
                        
            all_messages.extend(filtered_messages)
            # Note: Logging is handled by the calling service to avoid duplicate log entries
            
            # Update current_date to the oldest message date for the next iteration
            if oldest_message_date:
                current_date = oldest_message_date
                # Set offset_id to the ID of the oldest message to continue from there
                oldest_message = min(filtered_messages, key=lambda m: m.date) if filtered_messages else None
                if oldest_message:
                    offset_id = oldest_message.id
            else:
                # If no messages were found, move the date back by 30 days
                current_date -= datetime.timedelta(days=30)
                offset_id = 0
                
            # Add a small delay to avoid rate limiting
            await asyncio.sleep(1)
            
        # Only log the total if we actually retrieved messages
        if all_messages:
            tg_helper_logger.info("Retrieved total historical messages from channel", message_count=len(all_messages), channel_id=channel_id)
        else:
            tg_helper_logger.debug("No historical messages retrieved from channel", channel_id=channel_id)
            
        return all_messages
        
    except Exception as e:
        tg_helper_logger.error("Error getting historical messages from channel", channel_id=channel_id, error=str(e))
        return []

def force_release_database_lock(phone_number: str) -> bool:
    """
    Attempt to forcefully release a database lock by checking if it's actually in use.
    
    Args:
        phone_number (str): Phone number associated with the session
        
    Returns:
        bool: True if lock was successfully released or doesn't exist, False otherwise
    """
    try:
        session_path = f'sessions/session_{phone_number}'
        session_file_path = f'{session_path}.session'
        
        # If the file doesn't exist, there's no lock to release
        if not os.path.exists(session_file_path):
            return True
            
        # Try to connect to the database to see if it's actually locked
        try:
            conn = sqlite3.connect(session_file_path, timeout=1.0)
            cursor = conn.cursor()
            cursor.execute('PRAGMA quick_check')
            conn.close()
            # If we got here, the database is accessible
            return True
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e).lower():
                # Database is actually locked, we can't do much about it
                # except wait and retry
                tg_helper_logger.warning("Database is locked, cannot force release", phone_number=phone_number)
                return False
            else:
                # Some other error occurred
                tg_helper_logger.error("Unexpected database error for", phone_number=phone_number, error=str(e))
                return False
        except Exception as e:
            tg_helper_logger.error("Error checking database status for", phone_number=phone_number, error=str(e))
            return False
            
    except Exception as e:
        tg_helper_logger.error("Error in force_release_database_lock for", phone_number=phone_number, error=str(e))
        return False

def handle_database_lock(phone_number: str, max_retries: int = 3) -> bool:
    """
    Handle database lock issues by retrying or cleaning up session files with improved logic.

    Args:
        phone_number (str): Phone number associated with the session
        max_retries (int): Maximum number of retries

    Returns:
        bool: True if the issue was resolved, False otherwise
    """
    session_path = f'sessions/session_{phone_number}'
    session_file_path = f'{session_path}.session'

    for attempt in range(max_retries):
        try:
            # Check if session file exists
            if os.path.exists(session_file_path):
                # First, try to check if the database is actually locked
                if force_release_database_lock(phone_number):
                    return True

                # Try to remove the lock by closing any connections
                journal_file = f'{session_path}.session-journal'
                if os.path.exists(journal_file):
                    try:
                        os.remove(journal_file)
                        tg_helper_logger.info("Removed journal file", phone_number=phone_number)
                    except Exception as e:
                        tg_helper_logger.warning("Could not remove journal file", phone_number=phone_number, error=str(e))

                # Progressive wait time (2, 4, 6 seconds)
                wait_time = (attempt + 1) * 2
                tg_helper_logger.info(f"Waiting {wait_time}s before retry", phone_number=phone_number, attempt=attempt + 1)
                time.sleep(wait_time)

                # Test if the database is still locked with timeout
                try:
                    conn = sqlite3.connect(session_file_path, timeout=2.0)
                    conn.execute('SELECT count(*) FROM sessions')
                    conn.close()
                    tg_helper_logger.info("Database lock resolved", phone_number=phone_number)
                    return True  # Database is accessible
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e).lower():
                        tg_helper_logger.warning(f"Database still locked after attempt {attempt + 1}", phone_number=phone_number)
                    else:
                        tg_helper_logger.error("Unexpected database error", phone_number=phone_number, error=str(e))
            else:
                return True  # No session file, so no lock issue

        except Exception as e:
            tg_helper_logger.error("Error handling database lock for", phone_number=phone_number, error=str(e))

    tg_helper_logger.error(f"Failed to resolve database lock after {max_retries} attempts", phone_number=phone_number)
    return False

def wait_for_session_availability(phone_number: str, max_wait_seconds: int = 30) -> bool:
    """
    Wait for a session to become available by checking database lock status.

    Args:
        phone_number (str): Phone number associated with the session
        max_wait_seconds (int): Maximum seconds to wait

    Returns:
        bool: True if session became available, False if timeout
    """
    session_path = f'sessions/session_{phone_number}'
    session_file_path = f'{session_path}.session'

    if not os.path.exists(session_file_path):
        return True  # No session file means it's available

    start_time = time.time()
    check_interval = 1.0  # Check every second

    while time.time() - start_time < max_wait_seconds:
        try:
            # Try to access the database with a short timeout
            conn = sqlite3.connect(session_file_path, timeout=0.5)
            conn.execute('SELECT count(*) FROM sessions')
            conn.close()
            tg_helper_logger.info("Session became available", phone_number=phone_number)
            return True
        except sqlite3.OperationalError:
            # Still locked, wait and try again
            time.sleep(check_interval)

    tg_helper_logger.warning(f"Session did not become available within {max_wait_seconds}s", phone_number=phone_number)
    return False

def cleanup_session_files(phone_number: str) -> bool:
    """
    Clean up session files for a given phone number.
    
    Args:
        phone_number (str): Phone number associated with the session
        
    Returns:
        bool: True if cleanup was successful, False otherwise
    """
    try:
        session_path = f'sessions/session_{phone_number}'
        session_file_path = f'{session_path}.session'
        journal_file_path = f'{session_path}.session-journal'
        
        # Remove session file if it exists
        if os.path.exists(session_file_path):
            try:
                os.remove(session_file_path)
                tg_helper_logger.info("Removed session file", phone_number=phone_number)
            except Exception as e:
                tg_helper_logger.warning("Could not remove session file (might be in use)", phone_number=phone_number, error=str(e))
                return False
            
        # Remove journal file if it exists
        if os.path.exists(journal_file_path):
            try:
                os.remove(journal_file_path)
                tg_helper_logger.info("Removed journal file", phone_number=phone_number)
            except Exception as e:
                tg_helper_logger.warning("Could not remove journal file (might be in use)", phone_number=phone_number, error=str(e))
                return False
            
        return True
    except Exception as e:
        tg_helper_logger.error("Error cleaning up session files for", phone_number=phone_number, error=str(e))
        return False