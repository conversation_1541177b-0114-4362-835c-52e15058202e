"""
Message helper functions for the Telegram Scraper application.
This module contains utility functions for message operations.
"""
import time

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from utils.logger import Logger
from models.message import Message
from models.register import Register
from models.tg_entity import TgEntity
from models.register_tg_entity import RegisterTgEntity

message_helper_logger = Logger("message_helper")

async def create_initial_posting_message(session: AsyncSession, register: Register, are_new_entities: bool = False) -> Message:
    """
    Create an initial message in the message table indicating that posting will start 
    after the crawling process is done. Only creates message when channel/group provided is new.
    
    Args:
        session (AsyncSession): Database session
        register (Register): Register entry
        are_new_entities (bool): Whether the entities are new (default: False)
        
    Returns:
        Message: Created message object
    """
    # Only create message when entities are new
    if not are_new_entities:
        # Reduced logging - only log at debug level for routine operations
        message_helper_logger.debug(
            f"Skipping initial posting message for project {register.project_id} - no new entities"
        )
        return None
    
    # Per requirements, do not create messages during registration
    # Only crawling process should create message records
    message_helper_logger.debug(
        "Skipping initial posting message creation for project %s during registration", register.project_id
    )
    return None

async def check_crawling_completion_status(session: AsyncSession, project_id: int) -> bool:
    """
    Check if the crawling process is completed for a project by looking for 
    a completion message in the message table.
    
    Args:
        session (AsyncSession): Database session
        project_id (int): Project ID to check
        
    Returns:
        bool: True if crawling is completed, False otherwise
    """
    try:
        # Look for a crawling completion message
        stmt = select(Message).where(
            Message.project_id == project_id,
            Message.type == "crawling_completed"
        )
        result = await session.execute(stmt)
        completion_message = result.scalars().first()
        
        return completion_message is not None
        
    except Exception as e:
        message_helper_logger.error(
            "Error checking crawling completion status for project %s: %s", project_id, str(e)
        )
        return False

async def mark_crawling_as_completed(session: AsyncSession, project_id: int) -> Message:
    """
    Mark the crawling process as completed by adding a completion message.
    
    Args:
        session (AsyncSession): Database session
        project_id (int): Project ID
        
    Returns:
        Message: Created completion message object
    """
    try:
        # Create a message indicating that crawling is completed
        completion_message = Message(
            project_id=project_id,
            message_id=0,  # Special ID for system messages
            entities_id=None,  # Not tied to a specific entity
            type="crawling_completed",  # Special type for crawling completion
            content="Crawling process completed. Posting can now begin.",
            message="Crawling process completed. Posting can now begin.",
            message_used=None,
            reply_to=None,
            reply_time=None,
            created_at=int(time.time())
        )
        
        session.add(completion_message)
        await session.commit()
        await session.refresh(completion_message)
        
        message_helper_logger.info(
            "Marked crawling as completed for project %s", project_id
        )
        
        return completion_message
        
    except Exception as e:
        message_helper_logger.error(
            "Error marking crawling as completed for project %s: %s", project_id, str(e)
        )
        await session.rollback()
        raise

async def is_message_unique(session: AsyncSession, project_id: int, message_content: str, message_type: str) -> bool:
    """
    Check if a message with the same content and type already exists for the project.
    
    Args:
        session (AsyncSession): Database session
        project_id (int): Project ID
        message_content (str): Message content to check
        message_type (str): Message type to check
        
    Returns:
        bool: True if message is unique, False if it already exists
    """
    try:
        # Look for an existing message with the same content and type
        stmt = select(Message).where(
            Message.project_id == project_id,
            Message.content == message_content,
            Message.type == message_type
        )
        result = await session.execute(stmt)
        existing_message = result.scalars().first()
        
        is_unique = existing_message is None
        
        # Log detailed information about the uniqueness check only when needed
        if not is_unique:
            content_snippet = message_content[:50] + ('...' if len(message_content) > 50 else '')
            message_helper_logger.debug(
                "Message already exists for project %s - content: '%s', type: %s, existing_id: %s",
                project_id, content_snippet, message_type, existing_message.id
            )
        
        return is_unique
        
    except Exception as e:
        message_helper_logger.error(
            "Error checking message uniqueness for project %s: %s", project_id, str(e)
        )
        # If there's an error, assume the message is not unique to be safe
        return False

async def check_if_entities_are_new(session: AsyncSession, project_id: int, entity_names: list) -> bool:
    """
    Check if the provided entities are new (don't exist in the database).
    
    Args:
        session (AsyncSession): Database session
        project_id (int): Project ID
        entity_names (list): List of entity names to check
        
    Returns:
        bool: True if any entity is new, False if all entities already exist
    """
    try:
        # Check if any of the entities are new
        for entity_name in entity_names:
            stmt = select(TgEntity).where(TgEntity.name == entity_name)
            result = await session.execute(stmt)
            existing_entity = result.scalars().first()
            
            # If any entity doesn't exist, return True
            if not existing_entity:
                return True
                
        # Check if there are existing relationships for this project
        stmt = select(RegisterTgEntity).where(RegisterTgEntity.project_id == project_id)
        result = await session.execute(stmt)
        existing_relationships = result.scalars().all()
        
        # If no existing relationships and we have entities, they are new
        if not existing_relationships and entity_names:
            return True
            
        # If we have entities but no matching existing entities, they are new
        if entity_names and len(existing_relationships) != len(entity_names):
            return True
            
        return False
        
    except Exception as e:
        message_helper_logger.error(
            "Error checking if entities are new for project %s: %s", project_id, str(e)
        )
        # If there's an error, assume entities are new to be safe
        return True