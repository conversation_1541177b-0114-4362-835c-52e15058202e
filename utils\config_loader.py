import json
import os
from typing import Dict, Any

class ConfigLoader:
    @staticmethod
    def load_config(config_path: str = "config.json") -> Dict[str, Any]:
        """Load configuration from JSON file."""
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file {config_path} not found")

        with open(config_path, 'r') as f:
            return json.load(f)

    @staticmethod
    def get_database_url(config: Dict[str, Any]) -> str:
        """Construct database URL from config."""
        # Use the new config structure
        return f"mysql+aiomysql://{config['username']}:{config['password']}@{config['host']}:3306/{config['database']}"

    @staticmethod
    def get_crypto_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """Get crypto-specific configuration."""
        return config.get('crypto', {})

    @staticmethod
    def get_crypto_assets(config: Dict[str, Any]) -> list:
        """Get default crypto assets for analysis."""
        crypto_config = config.get('crypto', {})
        return crypto_config.get('default_assets', ['BTC', 'ETH'])

    @staticmethod
    def get_content_generation_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """Get content generation configuration."""
        crypto_config = config.get('crypto', {})
        return crypto_config.get('content_generation', {})

# Load configuration at application startup
config = ConfigLoader.load_config()