"""
State helper functions for the Telegram Scraper application.
This module contains utility functions for managing scraper state.
"""

import json
import os
import datetime
from typing import Dict, Any
from utils.logger import Logger

state_helper_logger = Logger("state_helper")

class PersistentStateManager:
    """Persistent state manager for tracking scraping progress"""
    
    def __init__(self, state_file: str = "scraper_state.json"):
        self.state_file = state_file
        self.state = self._load_state()
        
    def _load_state(self) -> Dict[str, Any]:
        """Load state from file or create new state if file doesn't exist"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    state = json.load(f)
                state_helper_logger.info("Loaded state from file with channels", file=self.state_file, channel_count=len(state.get('channel_states', {})))
                return state
            else:
                state_helper_logger.info("State file not found, creating new state", file=self.state_file)
                return {}
        except Exception as e:
            state_helper_logger.error("Error loading state from file", file=self.state_file, error=str(e))
            return {}
            
    def _save_state(self):
        """Save state to file"""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(self.state, f, indent=2)
            state_helper_logger.debug("Saved state to file with channels", file=self.state_file, channel_count=len(self.state.get('channel_states', {})))
        except Exception as e:
            state_helper_logger.error("Error saving state to file", file=self.state_file, error=str(e))
            
    def get_last_processed_id(self, channel_key: str) -> int:
        """Get the last processed message ID for a channel"""
        last_id = self.state.get('channel_states', {}).get(channel_key, 0)
        state_helper_logger.debug("Retrieved last processed ID", channel_key=channel_key, last_id=last_id)
        return last_id
        
    def update_last_processed_id(self, channel_key: str, message_id: int):
        """Update the last processed message ID for a channel"""
        current_id = self.get_last_processed_id(channel_key)
        if message_id > current_id:
            if 'channel_states' not in self.state:
                self.state['channel_states'] = {}
            self.state['channel_states'][channel_key] = message_id
            self._save_state()
            state_helper_logger.debug("Updated last processed ID", channel_key=channel_key, current_id=current_id, message_id=message_id)
        else:
            state_helper_logger.debug("Skipped updating last processed ID", channel_key=channel_key, message_id=message_id, current_id=current_id)
            
    def get_start_time(self, channel_key: str) -> int:
        """Get the start time for a channel (for historical scraping)"""
        start_time = self.state.get('channel_start_times', {}).get(channel_key, 0)
        state_helper_logger.debug("Retrieved start time", channel_key=channel_key, start_time=start_time)
        return start_time
        
    def set_historical_start_time(self, channel_key: str, days_back: int = 90):
        """
        Set the start time for historical scraping.
        
        Args:
            channel_key (str): Channel identifier
            days_back (int): Number of days to go back (default: 90 days = 3 months)
        """
        # Calculate the timestamp for the specified number of days ago
        historical_time = int((datetime.datetime.now() - datetime.timedelta(days=days_back)).timestamp())
        if 'channel_start_times' not in self.state:
            self.state['channel_start_times'] = {}
        self.state['channel_start_times'][channel_key] = historical_time
        self._save_state()
        state_helper_logger.info("Set historical start time", 
                                channel_key=channel_key, days_back=days_back, timestamp=historical_time)
        
    def get_last_scrape_time(self, channel_key: str) -> int:
        """Get the last scrape time for a channel"""
        last_scrape = self.state.get('channel_last_scrape', {}).get(channel_key, 0)
        state_helper_logger.debug("Retrieved last scrape time", channel_key=channel_key, last_scrape=last_scrape)
        return last_scrape
        
    def update_last_scrape_time(self, channel_key: str, scrape_time: int):
        """Update the last scrape time for a channel"""
        if 'channel_last_scrape' not in self.state:
            self.state['channel_last_scrape'] = {}
        self.state['channel_last_scrape'][channel_key] = scrape_time
        self._save_state()
        state_helper_logger.debug("Updated last scrape time", channel_key=channel_key, scrape_time=scrape_time)
