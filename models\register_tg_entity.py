from sqlalchemy import Index, <PERSON><PERSON>ger, BigInteger, Foreign<PERSON>ey, String, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship
from models.base import Base
from models.register import Register
from models.tg_entity import TgEntity

class RegisterTgEntity(Base):
    __tablename__ = 'register_tg_entities'
    __table_args__ = (
        Index('idx_register_tg_entities_project_id', 'project_id'),
        Index('idx_register_tg_entities_tg_entity_id', 'tg_entity_id'),
    )

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    project_id: Mapped[int] = mapped_column(Integer, ForeignKey('register.project_id'), nullable=False)
    tg_entity_id: Mapped[int] = mapped_column(Integer, ForeignKey('tg_entities.id'), nullable=False)
    message_thread_id: Mapped[int] = mapped_column(BigInteger, nullable=True)  # Message thread ID for subtopics
    
    # Relationships
    register: Mapped["Register"] = relationship("Register", backref="register_tg_entities")
    tg_entity: Mapped["TgEntity"] = relationship("TgEntity", backref="register_tg_entities")