# tg-scraper

A Python-based application for scraping data from Telegram channels and processing messages with Large Language Models (LLMs) for analysis and automated responses.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Technology Stack](#technology-stack)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Configuration](#configuration)
- [Database Setup](#database-setup)
- [Usage](#usage)
- [API Endpoints](#api-endpoints)
- [Services](#services)
- [Helpers](#helpers)
- [Models](#models)
- [Logging](#logging)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [License](#license)

## Overview

tg-scraper is a comprehensive solution for scraping Telegram channels and groups, processing the collected data with LLMs, and optionally posting analyzed content to other Telegram channels. The application provides a REST API for managing users, sessions, and configurations, along with background services for continuous scraping and processing.

## Features

- **Telegram Scraping**: Automatically scrape messages from Telegram channels and groups
- **LLM Integration**: Process messages with OpenAI GPT and Anthropic Claude models
- **Automated Posting**: Post analyzed content to Telegram channels with rate limiting
- **Session Management**: Secure session handling with OTP verification
- **Configurable Processing**: Customizable LLM prompts and processing rules per user
- **Database Storage**: Store messages, entities, and processing results in MySQL
- **Background Services**: Continuous scraping and processing with intelligent scheduling
- **Forum Support**: Handle Telegram forum topics and message threads
- **Reply Handling**: Process and respond to reply messages with context preservation
- **Content Quality Filtering**: Filter out placeholder and low-quality content
- **Historical Data**: Scrape historical messages for new entities

## Architecture

The application follows a modular architecture with clear separation of concerns:

```
tg-scraper/
├── api/              # REST API endpoints
├── services/         # Core service implementations
├── helpers/          # Utility functions and helpers
├── models/           # Database models
├── utils/            # Common utilities
├── migrations/       # Database migrations
└── sessions/         # Telegram session files
```

### Core Components

1. **API Layer**: FastAPI-based REST API for user management and configuration
2. **Background Services**: Asynchronous services for scraping and processing
3. **Database Layer**: SQLAlchemy ORM with MySQL backend
4. **LLM Integration**: LangChain and custom LLM helpers for AI processing
5. **Workflow Engine**: LangGraph-based workflows for complex processing
6. **Telegram Integration**: Telethon-based Telegram client

## Technology Stack

- **Language**: Python 3.8+
- **Framework**: FastAPI (API), Telethon (Telegram), SQLAlchemy (Database)
- **Database**: MySQL with async support
- **AI/ML**: LangChain, LangGraph, OpenAI GPT, Anthropic Claude
- **Asynchronous Processing**: asyncio, aiohttp
- **Task Queue**: Built-in async background services
- **Configuration**: JSON-based configuration
- **Logging**: Custom structured logging system
- **Database Migrations**: Alembic

## Project Structure

```
tg-scraper/
├── api/                    # REST API endpoints
│   ├── auth.py            # Authentication and session endpoints
│   ├── register.py        # User registration and management endpoints
│   └── routes.py          # API route definitions
├── services/               # Core service implementations
│   ├── background_scraper.py  # Telegram scraping service
│   ├── langchain_processor.py # LLM processing service
│   ├── langgraph_service.py   # LangGraph workflow service
│   ├── llm_service.py         # LLM service
│   └── database.py            # Database service
├── helpers/                # Utility functions and helpers
│   ├── langgraph/         # LangGraph workflow components
│   ├── llm/               # LLM integration helpers
│   ├── telegram_helpers.py# Telegram utility functions
│   ├── database_helpers.py# Database utility functions
│   └── ...                # Other helper modules
├── models/                 # Database models
│   ├── register.py        # User registration model
│   ├── tg_entity.py       # Telegram entity model
│   ├── message.py         # Message model
│   └── ...                # Other models
├── utils/                  # Common utilities
│   ├── logger.py          # Custom logging system
│   └── config_loader.py   # Configuration loader
├── migrations/             # Database migrations
├── sessions/               # Telegram session files
├── app.py                 # FastAPI application entry point
├── db_manager.py          # Database management utility
├── config.json            # Application configuration
└── requirements.txt       # Python dependencies
```

## Installation

### Prerequisites

- Python 3.8 or higher
- MySQL 5.7 or higher
- Telegram API credentials (api_id and api_hash)
- OpenAI/Anthropic API keys (optional, for LLM features)

### Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd tg-scraper
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Configure the application (see [Configuration](#configuration))

5. Set up the database (see [Database Setup](#database-setup))

## Configuration

The application is configured through `config.json`:

```json
{
  "host": "localhost",
  "database": "tg_scraper",
  "username": "tg_scraper_user",
  "password": "secure_password",
  "security": {
    "console_bearer_token": "your_secure_token"
  },
  "application": {
    "historical_scraping_days": 90,
    "processing_interval_minutes": 5
  }
}
```

### Configuration Options

- `host`: Database host
- `database`: Database name
- `username`: Database username
- `password`: Database password
- `security.console_bearer_token`: Bearer token for API authentication
- `application.historical_scraping_days`: Number of days to scrape historical data
- `application.processing_interval_minutes`: Interval between processing cycles

## Database Setup

### Automatic Setup

Run the database manager to automatically set up the database:

```bash
python db_manager.py setup
```

This will:
1. Create the database and user
2. Run all migrations
3. Set up necessary tables

### Manual Setup

1. Create the database and user in MySQL:
   ```sql
   CREATE DATABASE tg_scraper CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'tg_scraper_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT ALL PRIVILEGES ON tg_scraper.* TO 'tg_scraper_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. Run migrations:
   ```bash
   python db_manager.py migrate
   ```

### Database Migrations

Manage database migrations with the db_manager:

```bash
# Check migration status
python db_manager.py status

# Run migrations
python db_manager.py migrate

# Create new migration
python db_manager.py create "migration message"

# Fix migration issues
python db_manager.py fix
```

## Usage

### Starting the Application

1. Start the FastAPI server:
   ```bash
   uvicorn app:app --host 0.0.0.0 --port 8000
   ```

2. Start the background services:
   ```bash
   # The background services start automatically with the FastAPI app
   ```

### Basic Workflow

1. **Register a User**: Use the API to register a new user with Telegram credentials
2. **Verify Session**: Request and verify an OTP to create a Telegram session
3. **Fetch Channels**: Retrieve the list of channels the user has access to
4. **Configure Processing**: Set up processing rules and LLM configuration
5. **Automatic Scraping**: The background scraper will automatically scrape channels
6. **LLM Processing**: The processor will analyze messages with configured LLMs
7. **Posting**: Processed content will be posted to configured channels

## API Endpoints

### Authentication

- `POST /api/auth/check-session`: Check if a session file exists
- `POST /api/auth/request-otp`: Request OTP code for phone number
- `POST /api/auth/verify-otp`: Verify OTP code
- `POST /api/auth/channels`: Get all channels/groups for authenticated user

### Registration

- `POST /api/register/create`: Create a new register entry
- `POST /api/register/update`: Update an existing register entry
- `POST /api/register/list`: List all register entries
- `POST /api/register/delete`: Delete a register entry

All API endpoints require authentication with a bearer token in the Authorization header:
```
Authorization: Bearer your_secure_token
```

## Services

### Background Scraper Service

Continuously scrapes Telegram channels for new content:
- Scrapes entities directly instead of per user
- Uses intelligent rate limiting
- Handles historical data scraping
- Updates tg_entities with real information

### LLM Processing Service

Processes scraped messages with LLMs:
- Supports OpenAI GPT and Anthropic Claude models
- Configurable prompts per user
- Batch processing with rate limiting
- Content quality filtering

### LangGraph Service

Implements complex AI workflows:
- Stateful, multi-step processing
- Custom workflow creation
- Message processing workflows
- Crypto analysis workflows

### Database Service

Manages database connections:
- SQLAlchemy async engine
- Session management
- Connection pooling

See [services/README.md](services/README.md) for detailed service documentation.

## Helpers

### LLM Helpers

Integration with Large Language Models:
- OpenAI and Anthropic support
- Message processing functions
- Crypto analysis capabilities
- Content generation

See [helpers/llm/README.md](helpers/llm/README.md) for detailed LLM documentation.

### LangGraph Helpers

Workflow implementation components:
- Graph state management
- Workflow creation factory
- Specialized workflow types
- Node and edge management

See [helpers/langgraph/README.md](helpers/langgraph/README.md) for detailed LangGraph documentation.

### Other Helpers

- `telegram_helpers.py`: Telegram client creation and message fetching
- `database_helpers.py`: Database utility functions
- `message_helpers.py`: Message processing utilities
- `validation_helpers.py`: Data validation functions

## Models

Database models are defined in the `models/` directory:
- `register.py`: User registration and configuration
- `tg_entity.py`: Telegram entities (channels, groups)
- `message.py`: Message storage
- `tg_content.py`: Processed content
- `bot_state.py`: Bot state tracking
- `otp_request.py`: OTP request tracking

## Logging

The application uses a custom structured logging system:

```python
from utils.logger import Logger

logger = Logger("module_name")
logger.info("Message", key="value")
```

### Log Format

```
INFO: [2025-09-12 07:00:38 UTC] ---- Section Title ----
INFO: [2025-09-12 07:00:38 UTC] Log message - key: value, ...
INFO: [2025-09-12 07:00:38 UTC] ---- End Section Title ----
```

### Log Levels

- `INFO`: General information and process flow
- `ERROR`: Error conditions
- `WARNING`: Warning conditions
- `DEBUG`: Debug-level information

## Development

### Code Structure

Follow the modular structure with clear separation of concerns:
- API endpoints in `api/`
- Business logic in `services/`
- Utility functions in `helpers/`
- Data models in `models/`
- Common utilities in `utils/`

### Coding Standards

- Follow PEP 8 style guide
- Use type hints for function parameters and return values
- Write docstrings for all public functions and classes
- Use the custom logger for all logging
- Handle exceptions appropriately

### Adding New Features

1. Create new modules in appropriate directories
2. Follow existing patterns for API endpoints, services, and helpers
3. Add database models if needed
4. Create database migrations for schema changes
5. Update documentation

## Testing

### Unit Tests

Run unit tests with pytest:

```bash
pytest tests/
```

### Integration Tests

Integration tests are located in the `tests/integration/` directory.

## Deployment

### Production Deployment

1. Set up a production database
2. Configure production settings in `config.json`
3. Deploy with a WSGI server like Gunicorn:
   ```bash
   gunicorn -w 4 -k uvicorn.workers.UvicornWorker app:app
   ```

### Environment Variables

Set environment variables for production:
- `LOG_LEVEL`: Logging level (INFO, DEBUG, WARNING, ERROR)
- `DATABASE_URL`: Database connection URL

## Troubleshooting

### Common Issues

#### Database Connection Issues

- Verify database credentials in `config.json`
- Check if MySQL is running
- Ensure the database and user exist

#### Telegram Session Issues

- Delete session files in the `sessions/` directory
- Re-verify OTP to create a new session
- Check Telegram API credentials

#### LLM API Issues

- Verify API keys in register configuration
- Check provider status pages for outages
- Ensure API keys have necessary permissions

#### Rate Limiting

- The application implements rate limiting to prevent Telegram throttling
- Reduce processing frequency if hitting rate limits

### Debugging

Enable debug logging by setting the log level:
```bash
LOG_LEVEL=DEBUG python app.py
```

Check logs for detailed error information and stack traces.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write tests if applicable
5. Update documentation
6. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.