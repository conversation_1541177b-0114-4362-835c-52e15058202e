"""
Model selector for LLM services.
"""

from typing import <PERSON><PERSON>
from utils.config_loader import Config<PERSON>oader
from utils.logger import Logger

# Initialize logger
model_selector_logger = Logger("model_selector")

def get_llm_config_by_model_selection(model_selection: int, api_key: str) -> <PERSON><PERSON>[str, dict, str]:
    """
    Get LLM configuration based on model selection from register table.
    
    Args:
        model_selection: Model selection value from register table (1-5)
        api_key: API key for the selected provider
        
    Returns:
        Tuple containing (endpoint_url, headers, model_name)
        
    Model Selection Mapping:
        1: OpenAI (gpt-4.1)
        2: Anthropic (claude-3-5-haiku-20241022)
        3: Gemini (gemini-2.0-flash)
        4: X.AI (grok-3-latest)
        5: DeepSeek (deepseek-chat)
    """
    try:
        # Load configuration
        config = ConfigLoader.load_config()
        
        # Determine provider and model based on selection
        if model_selection == 1:
            # OpenAI
            endpoint_url = config.get('openai')
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
            model = "gpt-4.1"
            model_selector_logger.info("Selected OpenAI model: gpt-4.1")
            
        elif model_selection == 2:
            # Anthropic
            endpoint_url = config.get('anthropic')
            headers = {"Content-Type": "application/json", "x-api-key": api_key, "anthropic-version": "2023-06-01"}
            model = "claude-3-5-haiku-20241022"
            model_selector_logger.info("Selected Anthropic model: claude-3-5-haiku-20241022")
            
        elif model_selection == 3:
            # Gemini
            endpoint_url = config.get('gemini')
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
            model = "gemini-2.0-flash"
            model_selector_logger.info("Selected Gemini model: gemini-2.0-flash")
            
        elif model_selection == 4:
            # X.AI
            endpoint_url = config.get('xai')
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
            model = "grok-3-latest"
            model_selector_logger.info("Selected X.AI model: grok-3-latest")
            
        elif model_selection == 5:
            # DeepSeek
            endpoint_url = config.get('deepseek')
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
            model = "deepseek-chat"
            model_selector_logger.info("Selected DeepSeek model: deepseek-chat")
            
        else:
            # Default to OpenAI if invalid selection
            endpoint_url = config.get('openai')
            headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
            model = "gpt-4.1"
            model_selector_logger.warning("Invalid model selection, defaulting to OpenAI gpt-4.1", model_selection=model_selection)
        
        return (endpoint_url, headers, model)
        
    except Exception as e:
        model_selector_logger.error("Error getting LLM config by model selection", error=str(e))
        # Return default configuration in case of error
        config = ConfigLoader.load_config()
        endpoint_url = config.get('openai')
        headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}
        model = "gpt-4.1"
        return (endpoint_url, headers, model)

def get_llm_config_from_register(register_data: dict) -> Tuple[str, dict, str]:
    """
    Get LLM configuration from register data.
    
    Args:
        register_data: Dictionary containing register data with model_selection and llm_api_key
        
    Returns:
        Tuple containing (endpoint_url, headers, model_name)
    """
    try:
        model_selection = register_data.get('model_selection', 1)  # Default to OpenAI
        api_key = register_data.get('llm_api_key', '')
        
        return get_llm_config_by_model_selection(model_selection, api_key)
        
    except Exception as e:
        model_selector_logger.error("Error getting LLM config from register data", error=str(e))
        raise