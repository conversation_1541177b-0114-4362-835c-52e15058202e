"""
Factory for creating LLM helper instances.
"""

from typing import Dict, Any, Optional
from utils.config_loader import ConfigLoader
from utils.logger import Logger
from helpers.llm.core import <PERSON><PERSON><PERSON>el<PERSON>, LLMProvider

# Initialize logger
llm_factory_logger = Logger("llm_factory")

# Model mappings
OPENAI_MODELS = {
    0: "gpt-3.5-turbo",
    1: "gpt-4",
    2: "gpt-4-turbo"
}

ANTHROPIC_MODELS = {
    0: "claude-3-haiku-20240307",
    1: "claude-3-sonnet-20240229",
    2: "claude-3-opus-20240229"
}

def _calculate_enhanced_max_tokens(user_max_tokens: int) -> int:
    """
    Calculate enhanced max tokens, increasing by up to 50% but not exceeding model limits.
    
    Args:
        user_max_tokens: User-specified max tokens
        
    Returns:
        int: Enhanced max tokens
    """
    # Increase by up to 50% but not more than 50% of the user's max_tokens
    enhancement = min(int(user_max_tokens * 0.5), 5000)  # Cap at 5000 tokens
    enhanced_tokens = user_max_tokens + enhancement
    
    # Apply model-specific limits
    # OpenAI GPT models typically have context windows of 4096-128000 tokens
    # Anthropic Claude models typically have context windows of 100000-200000 tokens
    # For safety, we'll cap at reasonable limits
    max_allowed_tokens = 16000  # Conservative limit for most models
    
    return min(enhanced_tokens, max_allowed_tokens)

def create_llm_helper(model_selection: int, api_key: str, 
                     temperature: float = 0.7, max_tokens: int = 1000) -> LLMHelper:
    """
    Create an LLM helper based on the model selection.
    
    Args:
        model_selection: Model selection (0-2 for OpenAI, 3-5 for Anthropic)
        api_key: API key for the LLM provider
        temperature: Temperature setting for the LLM
        max_tokens: Maximum tokens for the response
        
    Returns:
        LLMHelper: Initialized LLM helper
    """
    try:
        # Load configuration
        config = ConfigLoader.load_config()
        timeout = config.get('llm_timeout', 240)
        
        # Determine provider and model based on selection
        if 0 <= model_selection <= 2:
            # OpenAI models
            provider = LLMProvider.OPENAI
            model_name = OPENAI_MODELS.get(model_selection, "gpt-3.5-turbo")
            llm_factory_logger.info("Creating OpenAI LLM helper with model", model_name=model_name)
            
        elif 3 <= model_selection <= 5:
            # Anthropic models
            provider = LLMProvider.ANTHROPIC
            model_name = ANTHROPIC_MODELS.get(model_selection - 3, "claude-3-haiku-20240307")
            llm_factory_logger.info("Creating Anthropic LLM helper with model", model_name=model_name)
            
        else:
            # Default to OpenAI if invalid selection
            provider = LLMProvider.OPENAI
            model_name = "gpt-3.5-turbo"
            llm_factory_logger.warning("Invalid model selection, defaulting to OpenAI", model_selection=model_selection, model_name=model_name)
        
        # Create and return the LLM helper
        llm_helper = LLMHelper(
            provider=provider,
            api_key=api_key,
            model_name=model_name,
            temperature=temperature,
            max_tokens=max_tokens,
            timeout=timeout
        )
        
        return llm_helper
        
    except Exception as e:
        llm_factory_logger.error("Error creating LLM helper", error=str(e))
        raise

def create_llm_helper_from_register(register_data: Dict[str, Any]) -> LLMHelper:
    """
    Create an LLM helper from register data with enhanced max_tokens.
    
    Args:
        register_data: Dictionary containing register data with LLM configuration
        
    Returns:
        LLMHelper: Initialized LLM helper
    """
    try:
        user_max_tokens = register_data.get('llm_max_token', 1000)
        # Enhance max_tokens by up to 20% but not more than 20% of user's max_tokens
        enhanced_max_tokens = _calculate_enhanced_max_tokens(user_max_tokens)
        
        llm_helper = create_llm_helper(
            model_selection=register_data.get('model_selection', 0),
            api_key=register_data.get('llm_api_key', ''),
            temperature=register_data.get('llm_temperature', 0.7),
            max_tokens=enhanced_max_tokens
        )
        
        llm_factory_logger.info("Created LLM helper with enhanced max_tokens", user_max_tokens=user_max_tokens, enhanced_max_tokens=enhanced_max_tokens)
        
        return llm_helper
        
    except Exception as e:
        llm_factory_logger.error("Error creating LLM helper from register data", error=str(e))
        raise

def create_llm_helper_for_telegram(register_data: Dict[str, Any], 
                                  message_thread_id: Optional[int] = None) -> LLMHelper:
    """
    Create an LLM helper specifically for Telegram operations with enhanced max_tokens.
    
    Args:
        register_data: Dictionary containing register data with LLM configuration
        message_thread_id: Optional message thread ID for subtopic-specific operations
        
    Returns:
        LLMHelper: Initialized LLM helper
    """
    try:
        user_max_tokens = register_data.get('llm_max_token', 1000)
        # Enhance max_tokens by up to 20% but not more than 20% of user's max_tokens
        enhanced_max_tokens = _calculate_enhanced_max_tokens(user_max_tokens)
        
        llm_helper = create_llm_helper(
            model_selection=register_data.get('model_selection', 0),
            api_key=register_data.get('llm_api_key', ''),
            temperature=register_data.get('llm_temperature', 0.7),
            max_tokens=enhanced_max_tokens
        )
        
        llm_factory_logger.info("Created Telegram-specific LLM helper with enhanced max_tokens", user_max_tokens=user_max_tokens, enhanced_max_tokens=enhanced_max_tokens)
        
        return llm_helper
        
    except Exception as e:
        llm_factory_logger.error("Error creating Telegram-specific LLM helper", error=str(e))
        raise

def create_llm_helper_for_crypto_analysis(register_data: Dict[str, Any]) -> LLMHelper:
    """
    Create an LLM helper specifically for crypto analysis operations.
    
    Args:
        register_data: Dictionary containing register data with LLM configuration
        
    Returns:
        LLMHelper: Initialized LLM helper optimized for crypto analysis
    """
    try:
        # Use a lower temperature for more consistent analysis
        # Increase max_tokens for detailed analysis, but respect user's limit if it's higher
        user_max_tokens = register_data.get('llm_max_token', 2000)
        max_tokens = max(user_max_tokens, 2000)
        
        # Apply enhancement
        enhanced_max_tokens = _calculate_enhanced_max_tokens(max_tokens)
        
        llm_helper = create_llm_helper(
            model_selection=register_data.get('model_selection', 1),  # Default to GPT-4 for better analysis
            api_key=register_data.get('llm_api_key', ''),
            temperature=0.3,  # Lower temperature for more focused analysis
            max_tokens=enhanced_max_tokens
        )
        
        llm_factory_logger.info("Created crypto analysis LLM helper with enhanced max_tokens", max_tokens=max_tokens, enhanced_max_tokens=enhanced_max_tokens)
        
        return llm_helper
        
    except Exception as e:
        llm_factory_logger.error("Error creating crypto analysis LLM helper", error=str(e))
        raise