"""
Crypto service for processing crypto-related messages and market analysis.
"""

import asyncio
import traceback
from typing import Dict, Any, List
from utils.logger import Logger
from helpers.langgraph.factory import create_crypto_analysis_workflow
from helpers.llm.factory import create_llm_helper_for_crypto_analysis
from helpers.llm.utils import extract_trading_signals_from_response

# Initialize logger
crypto_service_logger = Logger("crypto_service")

class CryptoService:
    """Service for crypto signal processing and analysis"""
    
    def __init__(self):
        """Initialize the Crypto service"""
        self.crypto_service_logger = crypto_service_logger
        
    async def process_crypto_signals(self, register_data: Dict[str, Any], 
                                   messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Process crypto signals from messages using Langgraph workflow.
        
        Args:
            register_data: Register data containing configuration
            messages: List of messages to analyze for crypto signals
            
        Returns:
            Dict[str, Any]: Processing result with crypto signals and analysis
        """
        try:
            with self.crypto_service_logger.begin_section("Crypto Analysis") as section:
                section.log_step("Processing crypto signals from messages")
                section.log_step(f"Message count: {len(messages)}")
            
            # Create crypto analysis workflow
            workflow = create_crypto_analysis_workflow("crypto_signal_processing")
            
            # Prepare input state
            input_state = {
                "messages": messages,
                "context": {
                    "timestamp": "2023-01-01T00:00:00Z",  # In a real implementation, this would be dynamic
                    "source": "telegram"
                }
            }
            
            # Run workflow
            result = await workflow.invoke(input_state)
            
            with self.crypto_service_logger.begin_section("Crypto Analysis") as section:
                section.log_step("Successfully processed crypto signals")
            
            return {
                'success': True,
                'crypto_signals': result.get('crypto_signals', []),
                'market_analysis': result.get('market_analysis', {}),
                'unique_content': result.get('unique_content', ''),
                'workflow_result': result
            }
            
        except Exception as e:
            with self.crypto_service_logger.begin_section("Crypto Analysis") as section:
                section.log_step("Error processing crypto signals")
                section.log_step(f"Error details - Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'crypto_signals': [],
                'market_analysis': {},
                'unique_content': '',
                'workflow_result': None
            }
            
    async def analyze_market_conditions(self, register_data: Dict[str, Any], 
                                      market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze market conditions using LLM.
        
        Args:
            register_data: Register data containing LLM configuration
            market_data: Dictionary containing market data to analyze
            
        Returns:
            Dict[str, Any]: Market analysis results
        """
        try:
            with self.crypto_service_logger.begin_section("Market Analysis") as section:
                section.log_step("Analyzing market conditions")
            
            # Create LLM helper optimized for crypto analysis
            llm_helper = create_llm_helper_for_crypto_analysis(register_data)
            
            # Generate general analysis
            general_analysis = await llm_helper.generate_crypto_analysis(
                market_data=market_data,
                analysis_type="general"
            )
            
            # Generate trading signals
            signal_analysis = await llm_helper.generate_crypto_analysis(
                market_data=market_data,
                analysis_type="trading_signal"
            )
            
            # Extract trading signals from response
            trading_signals = extract_trading_signals_from_response(signal_analysis['response'])
            
            # Generate sentiment analysis
            sentiment_analysis = await llm_helper.generate_crypto_analysis(
                market_data=market_data,
                analysis_type="sentiment"
            )
            
            result = {
                'success': True,
                'general_analysis': general_analysis,
                'signal_analysis': signal_analysis,
                'trading_signals': trading_signals,
                'sentiment_analysis': sentiment_analysis,
                'token_count': (
                    general_analysis.get('token_count', 0) +
                    signal_analysis.get('token_count', 0) +
                    sentiment_analysis.get('token_count', 0)
                )
            }
            
            with self.crypto_service_logger.begin_section("Market Analysis") as section:
                section.log_step("Successfully analyzed market conditions")
                section.log_step(f"Token count: {result['token_count']}")
            
            return result
            
        except Exception as e:
            with self.crypto_service_logger.begin_section("Market Analysis") as section:
                section.log_step("Error analyzing market conditions")
                section.log_step(f"Error details - Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'general_analysis': None,
                'signal_analysis': None,
                'trading_signals': [],
                'sentiment_analysis': None,
                'token_count': 0
            }
            
    async def generate_unique_content(self, register_data: Dict[str, Any], 
                                    topic: str, content_type: str = "article",
                                    style_guide: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate unique content using LLM.
        
        Args:
            register_data: Register data containing LLM configuration
            topic: Topic to generate content about
            content_type: Type of content to generate
            style_guide: Optional style guide for content generation
            
        Returns:
            Dict[str, Any]: Generated content
        """
        try:
            with self.crypto_service_logger.begin_section("Content Generation") as section:
                section.log_step("Generating unique content")
                section.log_step(f"Content details - Content type: {content_type}, Topic: {topic}")
            
            # Create LLM helper
            llm_helper = create_llm_helper_for_crypto_analysis(register_data)
            
            # Generate content
            content = await llm_helper.generate_unique_content(
                topic=topic,
                content_type=content_type,
                style_guide=style_guide
            )
            
            result = {
                'success': True,
                'content': content,
                'content_type': content_type,
                'topic': topic,
                'token_count': llm_helper.get_token_count(content)
            }
            
            with self.crypto_service_logger.begin_section("Content Generation") as section:
                section.log_step("Successfully generated unique content")
                section.log_step(f"Token count: {result['token_count']}")
            
            return result
            
        except Exception as e:
            with self.crypto_service_logger.begin_section("Content Generation") as section:
                section.log_step("Error generating unique content")
                section.log_step(f"Error details - Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'content': None,
                'content_type': content_type,
                'topic': topic,
                'token_count': 0
            }
            
    async def process_crypto_data_batch(self, register_data: Dict[str, Any], 
                                      crypto_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process a batch of crypto data for analysis.
        
        Args:
            register_data: Register data containing LLM configuration
            crypto_data_list: List of crypto data dictionaries to process
            
        Returns:
            List[Dict[str, Any]]: List of analysis results
        """
        try:
            with self.crypto_service_logger.begin_section("Batch Processing") as section:
                section.log_step("Processing batch of crypto data")
                section.log_step(f"Data count: {len(crypto_data_list)}")
            
            # Process data sequentially instead of concurrently to ensure proper context handling
            processed_results = []
            for i, crypto_data in enumerate(crypto_data_list):
                try:
                    result = await self.analyze_market_conditions(register_data, crypto_data)
                    processed_results.append(result)
                except Exception as e:
                    with self.crypto_service_logger.begin_section("Batch Processing") as section:
                        section.log_step("Error processing crypto data")
                        section.log_step(f"Error details - Index: {i}, Error: {str(e)}")
                    processed_results.append({
                        'success': False,
                        'error': str(e),
                        'general_analysis': None,
                        'signal_analysis': None,
                        'trading_signals': [],
                        'sentiment_analysis': None,
                        'token_count': 0
                    })
            
            with self.crypto_service_logger.begin_section("Batch Processing") as section:
                section.log_step("Completed batch processing")
                section.log_step(f"Processing summary - Successful count: {len([r for r in processed_results if r['success']])}")
            
            return processed_results
            
        except Exception as e:
            with self.crypto_service_logger.begin_section("Batch Processing") as section:
                section.log_step("Error processing crypto data batch")
                section.log_step(f"Error details - Error: {str(e)}")
            return [{
                'success': False,
                'error': str(e),
                'general_analysis': None,
                'signal_analysis': None,
                'trading_signals': [],
                'sentiment_analysis': None,
                'token_count': 0
            }] * len(crypto_data_list)