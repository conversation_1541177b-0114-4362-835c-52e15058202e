from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from models.base import Base
from models.register import Register

class BotState(Base):
    __tablename__ = 'bot_state'

    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    project_id: Mapped[int] = mapped_column(Integer, ForeignKey('register.project_id'), nullable=False, unique=True)
    last_post_time: Mapped[int] = mapped_column(BigInteger, nullable=True)
    last_reply_time: Mapped[int] = mapped_column(BigInteger, nullable=True)
    
    # Relationship to Register
    register: Mapped["Register"] = relationship("Register", backref="bot_state")