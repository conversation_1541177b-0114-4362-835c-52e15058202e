"""
Validation helper functions for the Telegram Scraper application.
This module contains utility functions for data validation in API endpoints.
"""

from fastapi import HTTPException, status
from helpers.api_helpers import create_error_response
import re

class ValidationChain:
    """Validation chain for a single field."""
    
    def __init__(self, field_name):
        self.field_name = field_name
        self.rules = []
        self.required = False
    
    def is_required(self):
        """Mark the field as required."""
        self.required = True
        return self
    
    def is_int(self):
        """Add integer validation rule."""
        self.rules.append(('int', 'Field must be an integer'))
        return self
    
    def is_string(self):
        """Add string validation rule."""
        self.rules.append(('string', 'Field must be a string'))
        return self
    
    def is_phone_number(self):
        """Add phone number validation rule."""
        self.rules.append(('phone_number', 'Field must be a valid phone number'))
        return self
    
    def is_length(self, min_length=None, max_length=None):
        """Add length validation rule."""
        self.rules.append(('length', f'Field length must be between {min_length} and {max_length}', min_length, max_length))
        return self
    
    def matches(self, pattern, message):
        """Add regex pattern validation rule."""
        self.rules.append(('regex', message, pattern))
        return self
    
    def is_length(self, min_length=None, max_length=None):
        """Add length validation rule."""
        self.rules.append(('length', f'Field length must be between {min_length} and {max_length}', min_length, max_length))
        return self
    
    def custom(self, validator_func, message):
        """Add custom validation rule."""
        self.rules.append(('custom', message, validator_func))
        return self
    
    def validate(self, value):
        """Validate the value against all rules."""
        errors = []
        
        # Check if required
        if self.required and (value is None or value == ''):
            errors.append(f'{self.field_name} is required')
            return errors  # No need to check other rules if required and missing
        
        # Skip validation if value is None or empty and not required
        if value is None or value == '':
            return errors
        
        # Apply all rules
        for rule in self.rules:
            rule_type = rule[0]
            message = rule[1]
            
            if rule_type == 'int':
                try:
                    int(value)
                except (ValueError, TypeError):
                    errors.append(message)
            
            elif rule_type == 'string':
                if not isinstance(value, str):
                    errors.append(message)
            
            elif rule_type == 'phone_number':
                if not isinstance(value, str) or not re.match(r'^\+?[1-9]\d{1,14}$', value):
                    errors.append(message)
            
            elif rule_type == 'length':
                min_length = rule[2]
                max_length = rule[3]
                if min_length is not None and len(str(value)) < min_length:
                    errors.append(message)
                if max_length is not None and len(str(value)) > max_length:
                    errors.append(message)
            
            elif rule_type == 'regex':
                pattern = rule[2]
                if not re.match(pattern, str(value)):
                    errors.append(message)
            
            elif rule_type == 'custom':
                validator_func = rule[2]
                try:
                    if not validator_func(value):
                        errors.append(message)
                except Exception:
                    errors.append(message)
        
        return errors

class ValidationResult:
    """Result of validation."""
    
    def __init__(self, errors=None):
        self.errors = errors or []
    
    def is_valid(self):
        """Check if validation passed."""
        return len(self.errors) == 0

def body(fields):
    """Create validation chains for the specified fields."""
    chains = {}
    for field in fields:
        chains[field] = ValidationChain(field)
    return chains

def validate_data(data, chains):
    """Validate data against validation chains."""
    errors = []
    
    for field_name, chain in chains.items():
        value = data.get(field_name)
        field_errors = chain.validate(value)
        errors.extend([f"{field_name}: {error}" for error in field_errors])
    
    return ValidationResult(errors)

def raise_validation_error(validation_result):
    """Raise HTTP exception with validation errors."""
    if not validation_result.is_valid():
        error_messages = [error for error in validation_result.errors]
        error_message = ", ".join(error_messages) if error_messages else "Validation failed"
        
        # Create error response with empty data field but with validation message
        error_response = create_error_response(
            error_message,
            "",
            400
        )
        
        raise HTTPException(
            status_code=400,
            detail=error_response.dict()
        )