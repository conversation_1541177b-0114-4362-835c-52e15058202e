"""
Content cleanup helper functions for the Telegram Scraper application.
This module contains utility functions for cleaning up empty or invalid content.
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, func, or_
from utils.logger import Logger
from models.tg_content import TgContent

content_cleanup_logger = Logger("content_cleanup")

async def cleanup_empty_tg_content(session: AsyncSession) -> int:
    """
    Clean up empty or whitespace-only content from tg_content table.
    
    Args:
        session (AsyncSession): Database session
        
    Returns:
        int: Number of records cleaned up
    """
    try:
        # Find empty content records
        stmt = select(TgContent).where(
            or_(
                TgContent.content == '',
                TgContent.content.is_(None),
                func.trim(TgContent.content) == ''  # Only whitespace
            )
        )
        result = await session.execute(stmt)
        empty_records = result.scalars().all()
        
        if not empty_records:
            content_cleanup_logger.info("No empty content records found")
            return 0
        
        # Log details about what will be cleaned up
        content_cleanup_logger.info(
            "Found empty content records to clean up", 
            count=len(empty_records)
        )
        
        # Delete empty content records
        delete_stmt = delete(TgContent).where(
            or_(
                TgContent.content == '',
                TgContent.content.is_(None),
                func.trim(TgContent.content) == ''
            )
        )
        result = await session.execute(delete_stmt)
        deleted_count = result.rowcount
        
        await session.commit()
        
        content_cleanup_logger.info(
            "Successfully cleaned up empty content records", 
            deleted_count=deleted_count
        )
        
        return deleted_count
        
    except Exception as e:
        content_cleanup_logger.error(
            "Error cleaning up empty content records", 
            error=str(e)
        )
        await session.rollback()
        raise

async def validate_tg_content_integrity(session: AsyncSession) -> dict:
    """
    Validate the integrity of tg_content records.
    
    Args:
        session (AsyncSession): Database session
        
    Returns:
        dict: Validation results
    """
    try:
        # Count total records
        total_stmt = select(TgContent)
        total_result = await session.execute(total_stmt)
        total_count = len(total_result.scalars().all())
        
        # Count empty content records
        empty_stmt = select(TgContent).where(
            or_(
                TgContent.content == '',
                TgContent.content.is_(None),
                func.trim(TgContent.content) == ''
            )
        )
        empty_result = await session.execute(empty_stmt)
        empty_count = len(empty_result.scalars().all())
        
        # Count very short content (less than 3 characters)
        short_stmt = select(TgContent).where(
            func.length(TgContent.content) < 3
        )
        short_result = await session.execute(short_stmt)
        short_count = len(short_result.scalars().all())
        
        validation_results = {
            'total_records': total_count,
            'empty_records': empty_count,
            'short_records': short_count,
            'valid_records': total_count - empty_count,
            'integrity_score': ((total_count - empty_count) / total_count * 100) if total_count > 0 else 100
        }
        
        content_cleanup_logger.info(
            "Content integrity validation completed",
            **validation_results
        )
        
        return validation_results
        
    except Exception as e:
        content_cleanup_logger.error(
            "Error validating content integrity", 
            error=str(e)
        )
        raise
