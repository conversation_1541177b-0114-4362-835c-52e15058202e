from utils.logger import Logger
from helpers.auth_helpers import (
    verify_password, 
    get_password_hash, 
    create_access_token, 
    verify_token,
    generate_otp
)

auth_logger = Logger("auth")

class AuthService:
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify a plain password against a hashed password."""
        auth_logger.debug("Verifying password")
        return verify_password(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """Hash a plain password."""
        auth_logger.debug("Hashing password")
        return get_password_hash(password)

    @staticmethod
    def create_access_token(data: dict, expires_delta: int = None) -> str:
        """Create a JWT access token."""
        auth_logger.debug("Creating access token")
        return create_access_token(data, expires_delta)

    @staticmethod
    def verify_token(token: str) -> dict:
        """Verify a JWT token and return the payload."""
        auth_logger.debug("Verifying access token")
        payload = verify_token(token)
        if payload is None:
            auth_logger.error("Token verification failed")
        return payload

    @staticmethod
    def generate_otp() -> str:
        """Generate a random OTP code."""
        auth_logger.debug("Generating OTP")
        return generate_otp()