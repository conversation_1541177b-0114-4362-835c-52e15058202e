"""
Langgraph service for processing messages with Langgraph workflows.
"""

import asyncio
import traceback
from typing import Dict, Any, Optional, List
from utils.logger import Logger
from helpers.langgraph.factory import create_workflow, create_custom_workflow

# Initialize logger
langgraph_service_logger = Logger("langgraph_service")

class LanggraphService:
    """Service for Langgraph operations"""
    
    def __init__(self):
        """Initialize the Langgraph service"""
        self.langgraph_service_logger = langgraph_service_logger
        
    async def create_and_run_workflow(self, workflow_type: str = "default", 
                                     name: str = "workflow",
                                     input_state: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create and run a Langgraph workflow.
        
        Args:
            workflow_type: Type of workflow to create
            name: Name of the workflow
            input_state: Initial state for the workflow
            
        Returns:
            Dict[str, Any]: Final state after workflow execution
        """
        try:
            with self.langgraph_service_logger.begin_section("Workflow Execution") as section:
                section.log_step("Creating and running workflow")
                section.log_step(f"Workflow details - Name: {name}")
            
            if input_state is None:
                input_state = {}
                
            # Create workflow
            workflow = create_workflow(workflow_type, name)
            
            # Run workflow
            result = await workflow.invoke(input_state)
            
            with self.langgraph_service_logger.begin_section("Workflow Execution") as section:
                section.log_step("Successfully executed workflow")
                section.log_step(f"Workflow details - Name: {name}")
            
            return {
                'success': True,
                'workflow_name': name,
                'result': result
            }
            
        except Exception as e:
            with self.langgraph_service_logger.begin_section("Workflow Execution") as section:
                section.log_step("Error creating and running workflow")
                section.log_step(f"Error details - Name: {name}, Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'workflow_name': name,
                'result': None
            }
            
    async def run_message_processing_workflow(self, messages: List[Dict[str, str]], 
                                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run a message processing workflow with optional date range support.
        
        Args:
            messages: List of messages to process
            context: Additional context for processing, including date_range if needed
            
        Returns:
            Dict[str, Any]: Processing result
        """
        try:
            with self.langgraph_service_logger.begin_section("Message Processing") as section:
                section.log_step("Running message processing workflow")
                section.log_step(f"Message count: {len(messages)}")
            
            if context is None:
                context = {}
                
            # Add date information to context if not present
            if 'processing_timestamp' not in context:
                from datetime import datetime
                context['processing_timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
            # Add date context information if available
            if 'date_context' not in context:
                if 'date_range' in context:
                    date_range = context['date_range']
                    start_date = date_range.get('start')
                    end_date = date_range.get('end')
                    if start_date and end_date:
                        context['date_context'] = f"This analysis covers messages from {start_date} to {end_date}"
                    elif start_date:
                        context['date_context'] = f"This analysis covers messages from {start_date} onwards"
                    elif end_date:
                        context['date_context'] = f"This analysis covers messages up to {end_date}"
                    else:
                        context['date_context'] = "This analysis covers historical messages"
                else:
                    # Default to current timestamp
                    context['date_context'] = f"This analysis was performed on {context['processing_timestamp']}"
            
            # Process messages sequentially to ensure proper context handling
            processed_messages = []
            for message in messages:
                # Prepare input state for each message
                input_state = {
                    "messages": [message],
                    "context": context.copy()  # Create a copy to avoid context pollution
                }
                
                # Determine workflow type based on context
                workflow_type = "telegram" if 'message_thread_id' in context or 'date_range' in context or 'date_context' in context else "message_processing"
                
                # Create and run message processing workflow for each message
                result = await self.create_and_run_workflow(
                    workflow_type=workflow_type,
                    name="message_processing_workflow",
                    input_state=input_state
                )
                
                processed_messages.append(result)
            
            # For backward compatibility, return the result of the last message processed
            final_result = processed_messages[-1] if processed_messages else {
                'success': False,
                'error': 'No messages to process',
                'workflow_name': 'message_processing_workflow',
                'result': None
            }
            
            with self.langgraph_service_logger.begin_section("Message Processing") as section:
                section.log_step("Completed message processing workflow")
            
            return final_result
            
        except Exception as e:
            with self.langgraph_service_logger.begin_section("Message Processing") as section:
                section.log_step("Error in message processing workflow")
                section.log_step(f"Error details - Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'workflow_name': 'message_processing_workflow',
                'result': None
            }
            
    async def run_custom_workflow(self, name: str, 
                                 nodes: Dict[str, Any],
                                 edges: list, 
                                 entry_point: str,
                                 finish_point: str,
                                 input_state: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Run a custom Langgraph workflow.
        
        Args:
            name: Name of the workflow
            nodes: Dictionary of node names and their corresponding functions
            edges: List of tuples representing edges (start_node, end_node)
            entry_point: Name of the entry point node
            finish_point: Name of the finish point node
            input_state: Initial state for the workflow
            
        Returns:
            Dict[str, Any]: Final state after workflow execution
        """
        try:
            with self.langgraph_service_logger.begin_section("Custom Workflow") as section:
                section.log_step("Running custom workflow")
                section.log_step(f"Workflow details - Name: {name}")
            
            if input_state is None:
                input_state = {}
                
            # Create custom workflow
            workflow = create_custom_workflow(name, nodes, edges, entry_point, finish_point)
            
            # Run workflow
            result = await workflow.invoke(input_state)
            
            with self.langgraph_service_logger.begin_section("Custom Workflow") as section:
                section.log_step("Successfully executed custom workflow")
                section.log_step(f"Workflow details - Name: {name}")
            
            return {
                'success': True,
                'workflow_name': name,
                'result': result
            }
            
        except Exception as e:
            with self.langgraph_service_logger.begin_section("Custom Workflow") as section:
                section.log_step("Error running custom workflow")
                section.log_step(f"Error details - Name: {name}, Error: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'workflow_name': name,
                'result': None
            }