from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import JSONResponse
from utils.logger import Logger
from contextlib import asynccontextmanager
import asyncio

api_logger = Logger("api")

def create_app(config: dict, lifespan=None) -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title="Telegram Scraper API",
        description="REST API for Telegram scraping with AI capabilities",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan,  # Use provided lifespan or default to None
        contact={
            "name": "API Support",
            "url": "https://github.com/your-org/tg-scraper/issues",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "Apache 2.0",
            "url": "https://www.apache.org/licenses/LICENSE-2.0.html",
        }
    )

    # Custom exception handler for HTTP exceptions
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        # If the exception detail is already in our standard format, return it directly
        if isinstance(exc.detail, dict) and all(key in exc.detail for key in ["message", "error", "status"]):
            return JSONResponse(
                status_code=exc.status_code,
                content=exc.detail
            )
        # Otherwise, return the default format
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail}
        )

    # Include routers
    from api.auth import router as auth_router
    from api.register import router as register_router

    app.include_router(auth_router, prefix="/api/auth")
    app.include_router(register_router, prefix="/api/register")

    @app.get("/",
             summary="API Root",
             description="Root endpoint for the Telegram Scraper API",
             response_description="Welcome message")
    async def root():
        return {"message": "Telegram Scraper API"}

    @app.get("/health",
             summary="Health Check",
             description="Health check endpoint to verify that the API is running",
             response_description="Health status")
    async def health_check():
        return {"status": "healthy"}

    return app